package dev.journey.PathSeeker.modules.exploration;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import baritone.api.BaritoneAPI;
import baritone.api.IBaritone;
import baritone.api.pathing.goals.Goal;
import baritone.api.pathing.goals.GoalBlock;
import baritone.api.pathing.goals.GoalNear;
import baritone.api.pathing.goals.GoalXZ;
import baritone.api.process.IBaritoneProcess;
import baritone.api.process.PathingCommand;
import baritone.api.process.PathingCommandType;
import dev.journey.PathSeeker.modules.automation.AFKVanillaFly;
import meteordevelopment.meteorclient.systems.modules.Modules;
import net.minecraft.client.util.math.MatrixStack;
import com.github.benmanes.caffeine.cache.Cache;
import net.minecraft.client.gl.Framebuffer;
import net.minecraft.client.render.VertexConsumerProvider;
import com.github.benmanes.caffeine.cache.Caffeine;
import dev.journey.PathSeeker.PathSeeker;
import it.unimi.dsi.fastutil.objects.ReferenceOpenHashSet;
import it.unimi.dsi.fastutil.objects.ReferenceSet;
import meteordevelopment.meteorclient.events.render.Render3DEvent;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.color.Color;
import meteordevelopment.meteorclient.utils.render.color.SettingColor;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import meteordevelopment.meteorclient.utils.render.MeteorToast;
import net.minecraft.item.Items;
import org.lwjgl.glfw.GLFW;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraft.text.Text;
import net.minecraft.world.biome.Biome;
import net.minecraft.world.biome.BiomeKeys;
import net.minecraft.world.chunk.WorldChunk;
import xaeroplus.XaeroPlus;
import xaeroplus.event.ChunkDataEvent;
import xaeroplus.module.ModuleManager;
import xaeroplus.module.impl.PaletteNewChunks;
import xaeroplus.util.ChunkScanner;
import xaeroplus.util.ChunkUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class SearchBot extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();
    private final SettingGroup sgGrid = settings.createGroup("Grid Settings");
    private final SettingGroup sgNodes = settings.createGroup("Node Settings");
    private final SettingGroup sgMovement = settings.createGroup("Movement");
    private final SettingGroup sgPathfinding = settings.createGroup("Pathfinding");
    private final SettingGroup sgVisualization = settings.createGroup("Visualization");
    private final SettingGroup sgConstraints = settings.createGroup("Constraints");
    private final SettingGroup sgTyping = settings.createGroup("Command Typing");
    private final SettingGroup sgFlight = settings.createGroup("Flight Settings");
    private final SettingGroup sgTrail = settings.createGroup("Trail Settings");
    private final SettingGroup sgRender = settings.createGroup("Render");
    private final SettingGroup sgDebug = settings.createGroup("Debug");
    private final SettingGroup sgAdvanced = settings.createGroup("Advanced", false);

    // Pathfinding Settings
    private final Setting<PathfindingAlgorithm> algorithm = sgPathfinding.add(new EnumSetting.Builder<PathfindingAlgorithm>()
        .name("Algorithm")
        .description("The pathfinding algorithm to use")
        .defaultValue(PathfindingAlgorithm.A_STAR)
        .build()
    );

    private final Setting<Boolean> allowDiagonal = sgPathfinding.add(new BoolSetting.Builder()
        .name("Allow Diagonal")
        .description("Allow diagonal movement between nodes")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> maxPathLength = sgPathfinding.add(new IntSetting.Builder()
        .name("Max Path Length")
        .description("Maximum number of nodes in a single path")
        .defaultValue(50)
        .min(5)
        .sliderMax(200)
        .build()
    );

    // Visualization Settings
    private final Setting<SettingColor> currentPathColor = sgVisualization.add(new ColorSetting.Builder()
        .name("Current Path")
        .description("Color for the current path")
        .defaultValue(new SettingColor(0, 255, 0, 180))
        .build()
    );

    private final Setting<SettingColor> visitedNodeColor = sgVisualization.add(new ColorSetting.Builder()
        .name("Visited Nodes")
        .description("Color for visited nodes")
        .defaultValue(new SettingColor(0, 0, 255, 180))
        .build()
    );

    private final Setting<SettingColor> plannedNodeColor = sgVisualization.add(new ColorSetting.Builder()
        .name("Planned Nodes")
        .description("Color for planned but unvisited nodes")
        .defaultValue(new SettingColor(255, 165, 0, 180))
        .build()
    );

    private final Setting<SettingColor> obstacleColor = sgVisualization.add(new ColorSetting.Builder()
        .name("Obstacles")
        .description("Color for obstacles and walls")
        .defaultValue(new SettingColor(255, 0, 0, 180))
        .build()
    );

    // Constraint Settings
    private final Setting<Integer> wallDistance = sgConstraints.add(new IntSetting.Builder()
        .name("Wall Distance")
        .description("Minimum distance to maintain from walls")
        .defaultValue(2)
        .range(1, 10)
        .build()
    );

    private final Setting<Double> obstacleRatio = sgConstraints.add(new DoubleSetting.Builder()
        .name("Obstacle Density")
        .description("Ratio of grid cells that are obstacles (0-1)")
        .defaultValue(0.1)
        .range(0, 1)
        .sliderRange(0, 1)
        .build()
    );

    private final Setting<NodeSelectionMode> nodeSelection = sgConstraints.add(new EnumSetting.Builder<NodeSelectionMode>()
        .name("Node Selection")
        .description("How to select which nodes to visit")
        .defaultValue(NodeSelectionMode.ALL)
        .build()
    );

    public enum PathfindingAlgorithm {
        A_STAR("A*"),
        DIJKSTRA("Dijkstra's"),
        GREEDY_BFS("Greedy Best-First"),
        JPS("Jump Point Search");

        final String description;
        PathfindingAlgorithm(String description) {
            this.description = description;
        }
    }

    public enum NodeSelectionMode {
        ALL("Visit all nodes"),
        SPECIFIED("Visit specified targets only"),
        DYNAMIC("Dynamically select targets");

        final String description;
        NodeSelectionMode(String description) {
            this.description = description;
        }
    }

    // Command Typing Settings
    private final Setting<Integer> preCommandDelay = sgTyping.add(new IntSetting.Builder()
        .name("Pre-Command Delay")
        .description("Delay before starting command sequence")
        .defaultValue(10)
        .min(1)
        .sliderMax(50)
        .build()
    );

    private final Setting<Integer> charDelay = sgTyping.add(new IntSetting.Builder()
        .name("Character Delay")
        .description("Delay between typing each character")
        .defaultValue(2)
        .min(1)
        .sliderMax(10)
        .build()
    );

    private final Setting<Integer> postCommandDelay = sgTyping.add(new IntSetting.Builder()
        .name("Post-Command Delay")
        .description("Delay after completing command")
        .defaultValue(10)
        .min(1)
        .sliderMax(50)
        .build()
    );

    private final Setting<Boolean> manualEnter = sgTyping.add(new BoolSetting.Builder()
        .name("Manual Enter")
        .description("Wait for manual enter press")
        .defaultValue(false)
        .build()
    );

    // Movement Settings
    private final Setting<Double> movementSpeed = sgMovement.add(new DoubleSetting.Builder()
        .name("Movement Speed")
        .description("Base movement speed in blocks per second")
        .defaultValue(4.3)
        .min(0.1)
        .sliderMax(10)
        .build()
    );

    private final Setting<MovementPattern> searchPattern = sgMovement.add(new EnumSetting.Builder<MovementPattern>()
        .name("Search Pattern")
        .description("Pattern to use when searching grid")
        .defaultValue(MovementPattern.SPIRAL)
        .build()
    );

    private final Setting<Integer> patternSpacing = sgMovement.add(new IntSetting.Builder()
        .name("Pattern Spacing")
        .description("Spacing between pattern lines in chunks")
        .defaultValue(4)
        .min(1)
        .sliderMax(16)
        .build()
    );

    // Node Settings
    private final Setting<NodeVisitOrder> visitOrder = sgNodes.add(new EnumSetting.Builder<NodeVisitOrder>()
        .name("Visit Order")
        .description("Order in which to visit grid nodes")
        .defaultValue(NodeVisitOrder.SPIRAL)
        .build()
    );

    private final Setting<Integer> nodeSpacing = sgNodes.add(new IntSetting.Builder()
        .name("Node Spacing")
        .description("Distance between nodes in blocks")
        .defaultValue(32)
        .min(16)
        .sliderMax(128)
        .build()
    );

    private final Setting<SettingColor> nodeColor = sgNodes.add(new ColorSetting.Builder()
        .name("Node Color")
        .description("Color for grid nodes")
        .defaultValue(new SettingColor(0, 255, 255, 100))
        .build()
    );

    private final Setting<Boolean> showNodeConnections = sgNodes.add(new BoolSetting.Builder()
        .name("Show Connections")
        .description("Show lines between nodes")
        .defaultValue(true)
        .build()
    );

    private final Setting<DirectionPreference> directionPref = sgMovement.add(new EnumSetting.Builder<DirectionPreference>()
        .name("Direction Preference")
        .description("Preferred cardinal direction for exploration")
        .defaultValue(DirectionPreference.NONE)
        .build()
    );

    // Debug Settings
    private final Setting<Boolean> showCoords = sgDebug.add(new BoolSetting.Builder()
        .name("Show Coordinates")
        .description("Display current coordinates in HUD")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showPath = sgDebug.add(new BoolSetting.Builder()
        .name("Show Path")
        .description("Render exploration path")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> showToasts = sgDebug.add(new BoolSetting.Builder()
        .name("Show Toasts")
        .description("Display action notifications as toasts")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> debugMessages = sgDebug.add(new BoolSetting.Builder()
        .name("Debug Messages")
        .description("Show detailed debug info as toast notifications")
        .defaultValue(false)
        .build()
    );

    private final Setting<Boolean> crashPrevention = sgDebug.add(new BoolSetting.Builder()
        .name("Crash Prevention")
        .description("Enable enhanced error handling to prevent crashes")
        .defaultValue(true)
        .build()
    );

    private final Setting<Integer> autoSaveInterval = sgDebug.add(new IntSetting.Builder()
        .name("Auto Save Interval")
        .description("Seconds between automatic state saves (0 = disabled)")
        .defaultValue(60)
        .min(0)
        .sliderMax(300)
        .build()
    );

    private final Setting<Integer> minNodesToVisit = sgGeneral.add(new IntSetting.Builder()
        .name("Minimum Nodes to Visit")
        .description("Minimum number of nodes to visit before completion (0 = visit all)")
        .defaultValue(0)
        .min(0)
        .sliderMax(1000)
        .build()
    );

    private final Setting<Double> nodeReachDistance = sgMovement.add(new DoubleSetting.Builder()
        .name("Node Reach Distance")
        .description("Distance to consider a node as reached")
        .defaultValue(2.0)
        .min(1.0)
        .sliderMax(5.0)
        .build()
    );

    private final Setting<Boolean> showNodeProgress = sgVisualization.add(new BoolSetting.Builder()
        .name("Show Node Progress")
        .description("Show progress towards current target node")
        .defaultValue(true)
        .build()
    );

    private final Setting<Boolean> useElytraFlight = sgMovement.add(new BoolSetting.Builder()
        .name("Use Elytra Flight")
        .description("Use elytra flight for movement instead of walking")
        .defaultValue(false)
        .build()
    );

    private final Setting<Double> elytraSpeed = sgMovement.add(new DoubleSetting.Builder()
        .name("Elytra Speed")
        .description("Horizontal speed for elytra flight")
        .defaultValue(1.0)
        .min(0.1)
        .sliderMax(5.0)
        .build()
    );

    private final Setting<FlightMode> flightMode = sgMovement.add(new EnumSetting.Builder<FlightMode>()
        .name("Flight Mode")
        .description("Choose how SearchBot flies between nodes")
        .defaultValue(FlightMode.VANILLA)
        .build()
    );



    private final Setting<Boolean> useBaritone = sgMovement.add(new BoolSetting.Builder()
        .name("Use Baritone")
        .description("Use Baritone for pathfinding (fallback option)")
        .defaultValue(false)
        .build()
    );

    private final Setting<Integer> baritoneTimeout = sgMovement.add(new IntSetting.Builder()
        .name("Baritone Timeout")
        .description("Seconds to wait for Baritone to reach target before giving up")
        .defaultValue(30)
        .min(5)
        .sliderMax(120)
        .visible(() -> flightMode.get() == FlightMode.BARITONE)
        .build()
    );

    // General Settings
    private final Setting<Boolean> notifications = sgGeneral.add(new BoolSetting.Builder()
        .name("Progress Notifications")
        .description("Show exploration progress notifications.")
        .defaultValue(true)
        .build()
    );

    // Grid Settings
    private final Setting<Integer> gridChunkRadius = sgGrid.add(new IntSetting.Builder()
        .name("Grid Chunk Radius")
        .description("The radius of the search grid in chunks (16x16 blocks).")
        .defaultValue(16)
        .min(1)
        .sliderRange(1, 64)
        .build()
    );

    private final Setting<Integer> searchHeight = sgGrid.add(new IntSetting.Builder()
        .name("Search Height")
        .description("The height to fly at while searching.")
        .defaultValue(120)
        .min(0)
        .sliderMax(255)
        .build()
    );

    // Trail Settings
    private final Setting<Integer> maxTrailLength = sgTrail.add(new IntSetting.Builder()
        .name("Max Trail Length")
        .description("Maximum number of trail points to keep.")
        .defaultValue(20)
        .min(5)
        .sliderRange(5, 100)
        .build()
    );

    private final Setting<Integer> chunksBeforeStarting = sgTrail.add(new IntSetting.Builder()
        .name("Chunks Before Starting")
        .description("Number of chunks before detecting as a trail.")
        .defaultValue(10)
        .sliderRange(1, 50)
        .build()
    );

    private final Setting<Double> trailTimeout = sgTrail.add(new DoubleSetting.Builder()
        .name("Trail Timeout")
        .description("Time in MS before abandoning a trail.")
        .defaultValue(1000 * 30)
        .min(1000 * 10)
        .sliderMax(1000 * 60)
        .build()
    );

    private final Setting<DirectionWeighting> directionWeighting = sgTrail.add(new EnumSetting.Builder<DirectionWeighting>()
        .name("Direction Weighting")
        .description("How chunks should be weighted for path splits.")
        .defaultValue(DirectionWeighting.NONE)
        .build()
    );



    // Render Settings
    private final Setting<Boolean> renderGrid = sgRender.add(new BoolSetting.Builder()
        .name("Render Grid")
        .description("Renders the search grid.")
        .defaultValue(true)
        .build()
    );

    private final Setting<SettingColor> exploredColor = sgRender.add(new ColorSetting.Builder()
        .name("Explored Color")
        .description("Color for explored chunks.")
        .defaultValue(new SettingColor(0, 255, 0, 100))
        .build()
    );

    private final Setting<SettingColor> newColor = sgRender.add(new ColorSetting.Builder()
        .name("New Color")
        .description("Color for unexplored chunks.")
        .defaultValue(new SettingColor(255, 0, 0, 100))
        .build()
    );

    private final Setting<SettingColor> currentTargetColor = sgRender.add(new ColorSetting.Builder()
        .name("Target Color")
        .description("Color for current target chunk.")
        .defaultValue(new SettingColor(255, 165, 0, 100))
        .build()
    );

    private final Setting<SettingColor> gridLineColor = sgRender.add(new ColorSetting.Builder()
        .name("Grid Line Color")
        .description("Color for grid lines.")
        .defaultValue(new SettingColor(255, 255, 255, 100))
        .build()
    );

    // Advanced Settings
    private final Setting<Double> rotateScaling = sgAdvanced.add(new DoubleSetting.Builder()
        .name("Rotate Scaling")
        .description("Scaling of rotation speed (0-1).")
        .defaultValue(0.1)
        .range(0, 1)
        .build()
    );

    private final Setting<Integer> chunkCacheLength = sgAdvanced.add(new IntSetting.Builder()
        .name("Chunk Cache Length")
        .description("Number of chunks to cache.")
        .defaultValue(100_000)
        .range(0, 1_000_000)
        .build()
    );

    // State machine states
    private enum CommandState {
        IDLE,           // No commands being processed
        STOPPING_WALK,  // Disabling auto-walk before turn
        TURNING,        // Rotating to new direction
        STABILIZING,    // Waiting for movement to settle after turn
        RESUMING_WALK,  // Re-enabling auto-walk after turn
        WAITING        // Waiting for command completion
    }

    // Enhanced State tracking with persistence
    private boolean shouldCheckRotation = false;
    private CommandState commandState = CommandState.IDLE;
    private int commandTimer;
    private int waitTicks;
    private Direction pendingDirection;
    private ChunkPos startChunk;

    // State persistence for interruption recovery
    private ExplorationState savedState = null;
    private final Object stateLock = new Object();

    /**
     * Represents the complete exploration state for persistence
     */
    private record ExplorationState(
        List<PathNode> visitedNodes,
        List<PathNode> remainingNodes,
        BlockPos currentPosition,
        Direction currentDirection,
        ExplorationPhase phase,
        long startTime,
        double totalDistance,
        int exploredChunks,
        Map<String, Object> additionalData
    ) {
        public ExplorationState withVisitedNode(PathNode node) {
            List<PathNode> newVisited = new ArrayList<>(visitedNodes);
            newVisited.add(node);

            List<PathNode> newRemaining = new ArrayList<>(remainingNodes);
            newRemaining.removeIf(n -> n.pos().equals(node.pos()));

            return new ExplorationState(
                newVisited, newRemaining, currentPosition, currentDirection,
                phase, startTime, totalDistance, exploredChunks + 1, additionalData
            );
        }

        public double getCompletionPercentage() {
            int total = visitedNodes.size() + remainingNodes.size();
            return total > 0 ? (visitedNodes.size() * 100.0) / total : 0.0;
        }
    }

    // Coordinate tracking
    private record ExplorationPoint(double x, double z, long timestamp) {}
    private final List<ExplorationPoint> explorationPath = new ArrayList<>();
    private Vec3d lastPosition = null;
    private double totalDistance = 0;
    // Statistics tracking
    private record PathfinderStats(
        long startTime,
        int nodesVisited,
        int nodesRemaining,
        double currentPathLength,
        double totalDistance,
        long lastPathfindTime,
        long avgPathfindTime
    ) {}

    private PathfinderStats stats;
    private long lastStatsUpdate = 0;
    private static final long STATS_UPDATE_INTERVAL = 500; // Update every 500ms
    private long lastToastTime = 0;
    private static final long TOAST_COOLDOWN = 5000; // 5 seconds between toasts
    private final List<Vec3d> pathPoints = new ArrayList<>(); // For path visualization
    private final List<BlockPos> obstacles = new ArrayList<>(); // Track obstacles

    // Enhanced Performance tracking and optimization
    private final Queue<Long> pathfindTimes = new ArrayDeque<>();
    private static final int MAX_PATHFIND_SAMPLES = 20;

    // Pathfinding cache for performance optimization
    private final Map<PathCacheKey, List<PathNode>> pathCache = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;
    private static final long CACHE_EXPIRY_MS = 300000; // 5 minutes

    // Performance monitoring
    private long totalPathfindingTime = 0;
    private int pathfindingCalls = 0;
    private int cacheHits = 0;
    private int cacheMisses = 0;

    /**
     * Cache key for pathfinding results
     */
    private record PathCacheKey(
        BlockPos start,
        BlockPos target,
        PathfindingAlgorithm algorithm,
        int nodeSpacing,
        long timestamp
    ) {
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRY_MS;
        }
    }

    private void updateStats() {
        if (System.currentTimeMillis() - lastStatsUpdate < STATS_UPDATE_INTERVAL) return;

        int visited = (int) gridNodes.stream().filter(PathNode::visited).count();
        int remaining = gridNodes.size() - visited;
        double currentPathDist = calculatePathLength();

        // Calculate average pathfinding time
        long avgTime = pathfindTimes.isEmpty() ? 0 :
            pathfindTimes.stream().mapToLong(Long::longValue).sum() / pathfindTimes.size();

        stats = new PathfinderStats(
            startTime,
            visited,
            remaining,
            currentPathDist,
            totalDistance,
            pathfindTimes.isEmpty() ? 0 : pathfindTimes.peek(),
            avgTime
        );

        lastStatsUpdate = System.currentTimeMillis();
    }

    private double calculatePathLength() {
        if (currentPath == null || currentPath.isComplete()) return 0;

        double length = 0;
        List<PathNode> nodes = currentPath.nodes();
        for (int i = 0; i < nodes.size() - 1; i++) {
            Vec3d current = Vec3d.ofCenter(nodes.get(i).pos());
            Vec3d next = Vec3d.ofCenter(nodes.get(i + 1).pos());
            length += current.distanceTo(next);
        }
        return length;
    }

    private void updatePathVisualization() {
        pathPoints.clear();
        if (currentPath == null || currentPath.isComplete()) return;

        // Convert path nodes to smooth visualization points
        List<PathNode> nodes = currentPath.nodes();
        if (nodes.size() < 2) return;

        for (int i = 0; i < nodes.size() - 1; i++) {
            Vec3d start = Vec3d.ofCenter(nodes.get(i).pos());
            Vec3d end = Vec3d.ofCenter(nodes.get(i + 1).pos());

            // Add intermediate points for smoother lines
            int segments = Math.max(1, (int) start.distanceTo(end) / 2);
            for (int j = 0; j <= segments; j++) {
                double t = j / (double) segments;
                pathPoints.add(start.multiply(1 - t).add(end.multiply(t)));
            }
        }
    }


    // Navigation node system
    private record PathNode(
        BlockPos pos,           // Position in world
        Direction facing,       // Required rotation
        boolean requiresWalk,   // Whether auto-walk should be enabled
        boolean visited,        // Whether node has been visited
        List<PathNode> connections // Connected nodes
    ) {
        public PathNode withVisited(boolean newVisited) {
            return new PathNode(pos, facing, requiresWalk, newVisited, connections);
        }
    }

    private record NodePath(List<PathNode> nodes, int currentIndex) {
        public boolean isComplete() {
            return currentIndex >= nodes.size();
        }

        public PathNode current() {
            return isComplete() ? null : nodes.get(currentIndex);
        }

        public NodePath advance() {
            return new NodePath(nodes, currentIndex + 1);
        }
    }
    private final List<PathNode> gridNodes = new ArrayList<>();
    private PathNode currentNode = null;
    private int currentNodeIndex = 0;

    public enum NodeVisitOrder {
        SPIRAL("Spiral from center outward"),
        SNAKE("Snake pattern left to right"),
        NEAREST("Visit nearest unvisited"),
        QUADRANT("Quadrant by quadrant"),
        PERIMETER("Perimeter first then inward");

        final String description;
        NodeVisitOrder(String description) {
            this.description = description;
        }
    }

    // Movement tracking
    private double currentSpeed = 0;
    private int stuckTicks = 0;
    private static final int STUCK_THRESHOLD = 40; // 2 seconds
    private Vec3d lastValidPosition = null;

    private boolean isSpecifiedTarget(BlockPos pos) {
        return specifiedTargets.contains(pos);
    }

    // Direction offset helpers since Direction doesn't have getOffset methods
    private int getOffsetX(Direction dir) {
        return switch (dir) {
            case EAST -> 1;
            case WEST -> -1;
            default -> 0;
        };
    }

    private int getOffsetZ(Direction dir) {
        return switch (dir) {
            case SOUTH -> 1;
            case NORTH -> -1;
            default -> 0;
        };
    }

    // Command queue
    private final Queue<String> commandQueue = new ArrayDeque<>();
    private int lastCommandTick;
    private static final int MIN_COMMAND_INTERVAL = 10;
    private List<ChunkPos> unexploredChunks;
    private List<ChunkPos> completedChunks;
    private ChunkPos currentTarget;
    private ExplorationPhase currentPhase;
    private Direction currentDirection;
    private boolean autoWalkEnabled;
    private NodePath currentPath;
    private PathNode targetNode;
    private static final double NODE_REACH_DISTANCE = 3.0;
    private boolean followingTrail;
    private ArrayDeque<Vec3d> trail;
    private long lastFoundTrailTime;
    private double targetYaw;
    private int totalChunks;
    private int exploredCount;
    private boolean initialized;
    private Cache<Long, Byte> seenChunksCache;

    private boolean isNearBoundary(int x, int z) {
        return x <= minChunkX + 3 || x >= maxChunkX - 3 ||
               z <= minChunkZ + 3 || z >= maxChunkZ - 3;
    }

    private boolean isVeryCloseToBoundary(int x, int z) {
        return x <= minChunkX + 1 || x >= maxChunkX - 1 ||
               z <= minChunkZ + 1 || z >= maxChunkZ - 1;
    }

    private void transitionToGridSearch() {
        if (notifications.get()) {
            showToast("Navigation", "Transitioning to grid search", Items.COMPASS);
        }
        followingTrail = false;
        trail.clear();

        // Stop movement and reset states
        queueCommand(".toggle auto-walk off");
        autoWalkEnabled = false;
        commandState = CommandState.WAITING;
        waitTicks = 15;

        // Return to closest perimeter point
        ChunkPos playerChunk = mc.player.getChunkPos();
        Direction newDirection = calculateBoundaryTurn(playerChunk, playerChunk.x, playerChunk.z);
        queueCommand(".rotation set " + newDirection.command);
        currentDirection = newDirection;

        // Resume after rotation
        commandQueue.add(".toggle auto-walk on");
    }

    private Direction calculateBoundaryTurn(ChunkPos playerChunk, int projectedX, int projectedZ) {
        boolean isCorner = (projectedX <= minChunkX + 2 || projectedX >= maxChunkX - 2) &&
                          (projectedZ <= minChunkZ + 2 || projectedZ >= maxChunkZ - 2);

        if (isCorner) {
            return switch (currentDirection) {
                case NORTH -> Direction.EAST;
                case EAST -> Direction.SOUTH;
                case SOUTH -> Direction.WEST;
                case WEST -> Direction.NORTH;
            };
        } else {
            if (projectedX <= minChunkX + 2 || projectedX >= maxChunkX - 2) {
                return playerChunk.z < startChunk.z ? Direction.NORTH : Direction.SOUTH;
            } else {
                return playerChunk.x < startChunk.x ? Direction.EAST : Direction.WEST;
            }
        }
    }

    private void startTurnSequence(Direction newDirection) {
        if (commandState != CommandState.IDLE) {
            if (debugMessages.get()) {
                showToast("Turn Sequence", "Queued: " + newDirection.command, Items.COMPASS);
            }
            return;
        }

        // Calculate turn angle
        float currentYaw = (mc.player.getYaw() % 360 + 360) % 360;
        float targetYaw = newDirection.yaw;
        double turnAngle = Math.abs(currentYaw - targetYaw);
        if (turnAngle > 180) {
            turnAngle = 360 - turnAngle;
        }

        // Stop movement first
        commandState = CommandState.STOPPING_WALK;
        pendingDirection = newDirection;

        // Adjust timings based on turn angle
        int baseDelay = 5;
        commandTimer = baseDelay + (int)(turnAngle / 45); // More time for larger turns

        try {
            if (notifications.get()) {
                showToast("Direction Change",
                    "Turning " + (int)turnAngle + " degrees " + newDirection.command,
                    Items.COMPASS);
            }
        } catch (Exception e) {
            if (notifications.get()) {
                showToast("Direction Change", "Turning to " + newDirection.command, Items.COMPASS);
            }
        }

        // Ensure we're not moving too fast
        if (Math.abs(mc.player.getVelocity().horizontalLength()) > 0.2) {
            toggleAutoWalk(false);
            commandTimer += 10; // Extra time to slow down
        }
    }

    // Grid boundaries
    private int minChunkX;
    private int maxChunkX;
    private int minChunkZ;
    private int maxChunkZ;

    // Block detection sets for trail following
    private static final ReferenceSet<Block> OVERWORLD_BLOCKS = ReferenceOpenHashSet.of(
        Blocks.COPPER_ORE, Blocks.DEEPSLATE_COPPER_ORE, Blocks.AMETHYST_BLOCK,
        Blocks.SMOOTH_BASALT, Blocks.TUFF, Blocks.KELP, Blocks.KELP_PLANT,
        Blocks.POINTED_DRIPSTONE, Blocks.DRIPSTONE_BLOCK, Blocks.DEEPSLATE,
        Blocks.AZALEA, Blocks.BIG_DRIPLEAF, Blocks.BIG_DRIPLEAF_STEM,
        Blocks.SMALL_DRIPLEAF, Blocks.MOSS_BLOCK, Blocks.CAVE_VINES,
        Blocks.CAVE_VINES_PLANT
    );

    private static final ReferenceSet<Block> NETHER_BLOCKS = ReferenceOpenHashSet.of(
        Blocks.ANCIENT_DEBRIS, Blocks.BLACKSTONE, Blocks.BASALT,
        Blocks.CRIMSON_NYLIUM, Blocks.WARPED_NYLIUM, Blocks.NETHER_GOLD_ORE,
        Blocks.CHAIN
    );

    public enum Direction {
        NORTH("north", 180, 0, -1),
        EAST("east", 270, 1, 0),
        SOUTH("south", 0, 0, 1),
        WEST("west", 90, -1, 0);

        final String command;
        final int yaw;
        final int offsetX;
        final int offsetZ;

        Direction(String command, int yaw, int offsetX, int offsetZ) {
            this.command = command;
            this.yaw = yaw;
            this.offsetX = offsetX;
            this.offsetZ = offsetZ;
        }

        public int getOffsetX() {
            return this.offsetX;
        }

        public int getOffsetZ() {
            return this.offsetZ;
        }
    }

    private void renderHudText(MatrixStack matrices) {
        if (stats == null || !showToasts.get()) return;

        // Update HUD with important stats
        StringBuilder status = new StringBuilder();
        status.append("Explored: ").append(exploredCount).append("/").append(totalChunks);
        if (totalChunks > 0) {
            status.append(" (").append((int)((exploredCount * 100f) / totalChunks)).append("%)");
        }
        status.append("\nDistance: ").append((int)totalDistance).append("m");

        if (mc.player != null) {
            showToast("SearchBot Status", status.toString(), Items.COMPASS);
        }
    }

    public enum ExplorationPhase {
        PERIMETER,
        SNAKE_PATTERN,
        FILLING_GAPS,
        COMPLETED
    }



    public enum MovementPattern {
        SPIRAL("Spiral pattern from center"),
        SNAKE("Back-and-forth snake pattern"),
        QUADRANT("Quadrant-based exploration"),
        PERIMETER("Perimeter-first pattern");

        final String description;
        MovementPattern(String description) {
            this.description = description;
        }
    }

    public enum DirectionPreference {
        NONE("No preference"),
        NORTH_SOUTH("Prefer North-South"),
        EAST_WEST("Prefer East-West"),
        CLOCKWISE("Clockwise rotation"),
        COUNTER_CLOCKWISE("Counter-clockwise rotation");

        final String description;
        DirectionPreference(String description) {
            this.description = description;
        }
    }

    public enum DirectionWeighting {
        LEFT,
        NONE,
        RIGHT
    }

    private long startTime;
    private Set<BlockPos> specifiedTargets = new HashSet<>();

    // Flight system integration
    private boolean flightSystemActive = false;
    private AFKVanillaFly vanillaFly;

    // Baritone integration (fallback)
    private IBaritone baritone;
    private boolean baritoneActive = false;
    private long baritoneStartTime = 0;
    private BlockPos baritoneTarget = null;
    private Goal currentGoal = null;

    public SearchBot() {
        super(PathSeeker.Hunting, "SearchBot", "Systematically explores areas and follows trails.");
    }

    // Chat messages have been replaced with toast notifications
    // Commands use plain text without formatting

    @Override
    public void onActivate() {
        startTime = (int) (System.currentTimeMillis() / 1000);
        if (mc.player == null || mc.world == null) {
            showToast("Error", "Cannot activate - player or world is null", Items.BARRIER);
            this.toggle();
            return;
        }

        // Initialize collections
        commandQueue.clear();
        completedChunks = new ArrayList<>();
        unexploredChunks = new ArrayList<>();
        seenChunksCache = Caffeine.newBuilder()
            .maximumSize(chunkCacheLength.get())
            .expireAfterWrite(Duration.ofMinutes(30))
            .build();

        // Initialize flight systems
        initializeFlightSystems();

        // Initialize Baritone (fallback)
        if (useBaritone.get() || flightMode.get() == FlightMode.BARITONE) {
            try {
                baritone = BaritoneAPI.getProvider().getPrimaryBaritone();
                if (baritone != null) {
                    showToast("Baritone", "Baritone integration enabled", Items.COMPASS);
                } else {
                    showToast("Baritone Error", "Failed to get Baritone instance", Items.BARRIER);
                }
            } catch (Exception e) {
                showToast("Baritone Error", "Baritone not available: " + e.getMessage(), Items.BARRIER);
                baritone = null;
            }
        }

        // Try to restore previous state first
        boolean stateRestored = tryRestoreState();

        // Initialize state
        currentPhase = ExplorationPhase.PERIMETER;
        autoWalkEnabled = false;
        initialized = false;
        trail = new ArrayDeque<>();
        followingTrail = false;

        // Setup grid
        if (!stateRestored) {
            initializeGrid();
            generateGridNodes();
            if (notifications.get()) {
                showToast("SearchBot", "Starting fresh exploration", Items.COMPASS);
            }
        } else if (notifications.get()) {
            showToast("SearchBot", "Resumed previous session", Items.COMPASS);
        }

        // Initialize position
        BlockPos alignedPos = mc.player.getBlockPos();
        int spacing = nodeSpacing.get();
        alignedPos = new BlockPos(
            Math.round((float)alignedPos.getX() / spacing) * spacing,
            searchHeight.get(),
            Math.round((float)alignedPos.getZ() / spacing) * spacing
        );
        mc.player.setPosition(alignedPos.getX(), alignedPos.getY(), alignedPos.getZ());
        if (notifications.get()) {
            showToast("Grid Alignment", "Aligned to grid at " + alignedPos.toShortString(), Items.COMPASS);
        }

        // Move to closest perimeter point if not on perimeter
        ChunkPos playerChunk = mc.player.getChunkPos();
        if (!isOnPerimeter(playerChunk)) {
            if (notifications.get()) {
                showToast("Navigation", "Moving to perimeter", Items.DIAMOND_BOOTS);
            }
            moveToNearestPerimeterPoint();
        }

        // Setup initial direction
        currentDirection = calculateInitialDirection(playerChunk);
        queueCommand(".rotation set " + currentDirection.command);

        // Register events
        XaeroPlus.EVENT_BUS.register(this);
        initialized = true;
        startTime = System.currentTimeMillis();

        // Save initial state
        saveCurrentState();

        if (notifications.get()) {
            showToast("SearchBot", "Module activated - Starting exploration", Items.EMERALD);
        }

        // Test command system immediately
        if (debugMessages.get()) {
            showToast("Debug", "Testing command system...", Items.COMMAND_BLOCK);
            queueCommand(".rotation set north");
        }
    }

    private boolean isOnPerimeter(ChunkPos pos) {
        return pos.x == minChunkX || pos.x == maxChunkX ||
               pos.z == minChunkZ || pos.z == maxChunkZ;
    }

    private Direction calculateInitialDirection(ChunkPos pos) {
        if (pos.x == minChunkX) return Direction.NORTH;
        if (pos.x == maxChunkX) return Direction.SOUTH;
        if (pos.z == minChunkZ) return Direction.EAST;
        if (pos.z == maxChunkZ) return Direction.WEST;
        return Direction.NORTH;
    }

    @Override
    public void onDeactivate() {
        if (mc.player != null) {
            if (notifications.get()) {
                showToast("SearchBot", "Deactivating module", Items.REDSTONE);
            }

            // Cancel any pending command execution
            if (execState != CommandExecutionState.DONE) {
                execState = CommandExecutionState.DONE;
                execTimer = 0;
                pendingCommand = "";
                if (mc.currentScreen instanceof ChatScreen) {
                    mc.setScreen(null);
                }
            }

            // Ensure auto-walk is disabled
            if (autoWalkEnabled) {
                sendCommand(".toggle auto-walk off");
                autoWalkEnabled = false;
            }

            // Stop flight systems
            stopFlightSystems();

            // Stop Baritone if active
            if (baritone != null && baritoneActive) {
                try {
                    baritone.getPathingBehavior().cancelEverything();
                    baritoneActive = false;
                    showToast("Baritone", "Baritone pathfinding stopped", Items.REDSTONE);
                } catch (Exception e) {
                    showToast("Baritone Error", "Error stopping Baritone: " + e.getMessage(), Items.BARRIER);
                }
            }

            // Clear all command and state tracking
            commandQueue.clear();
            commandState = CommandState.IDLE;
            commandTimer = 0;
            waitTicks = 0;
            shouldCheckRotation = false;
        }

        try {
            // Cleanup collections
            if (trail != null) {
                trail.clear();
                trail = null;
            }
            if (unexploredChunks != null) {
                unexploredChunks.clear();
                unexploredChunks = null;
            }
            if (completedChunks != null) {
                completedChunks.clear();
                completedChunks = null;
            }
            if (seenChunksCache != null) {
                seenChunksCache.invalidateAll();
                seenChunksCache = null;
            }

            // Disable flight systems
            if (flightMode.get() == FlightMode.VANILLA || flightMode.get() == FlightMode.PITCH40) {
                AFKVanillaFly afkFly = Modules.get().get(AFKVanillaFly.class);
                if (afkFly != null && afkFly.isActive()) {
                    afkFly.toggle();
                }
            }
        } catch (Exception e) {
            showToast("Error", "Cleanup failed: " + e.getMessage(), Items.BARRIER);
        }

        // Always unregister from event bus
        XaeroPlus.EVENT_BUS.unregister(this);
        showToast("SearchBot", "Module deactivated successfully", Items.EMERALD);
    }

    private void setupFlight() {
        if ((flightMode.get() == FlightMode.VANILLA || flightMode.get() == FlightMode.PITCH40) && !mc.world.getDimension().hasCeiling()) {
            AFKVanillaFly afkFly = Modules.get().get(AFKVanillaFly.class);
            if (!afkFly.isActive()) afkFly.toggle();
        }
    }

    private void initializeGrid() {
        startChunk = mc.player.getChunkPos();

        // Calculate grid boundaries
        minChunkX = startChunk.x - gridChunkRadius.get();
        maxChunkX = startChunk.x + gridChunkRadius.get();
        minChunkZ = startChunk.z - gridChunkRadius.get();
        maxChunkZ = startChunk.z + gridChunkRadius.get();

        // Generate obstacles
        generateObstacles();

        // Generate grid nodes
        generateGridNodes();

        // Initialize perimeter chunks
        generatePerimeterChunks();

        currentPhase = ExplorationPhase.PERIMETER;
        currentDirection = Direction.NORTH;
        currentTarget = unexploredChunks.get(0);
        totalChunks = unexploredChunks.size();
        exploredCount = 0;

        // Initialize statistics
        stats = new PathfinderStats(
            System.currentTimeMillis(),
            0,
            gridNodes.size(),
            0,
            0,
            0,
            0
        );

        if (!gridNodes.isEmpty()) {
            currentNode = gridNodes.get(0);
            currentNodeIndex = 0;
        }

        if (notifications.get()) {
            showToast("Grid Setup", String.format("Initialized grid: %d chunks, %d nodes", totalChunks, gridNodes.size()), Items.MAP);
        }
    }

    private void generateObstacles() {
        obstacles.clear();
        int spacing = nodeSpacing.get();
        int gridWidth = (maxChunkX - minChunkX + 1) * 16;
        int gridLength = (maxChunkZ - minChunkZ + 1) * 16;
        int obstacleCount = (int) (gridWidth * gridLength * obstacleRatio.get() / (spacing * spacing));

        Random random = new Random();
        int minDist = wallDistance.get();

        while (obstacles.size() < obstacleCount) {
            int x = minChunkX * 16 + minDist + random.nextInt(gridWidth - 2 * minDist);
            int z = minChunkZ * 16 + minDist + random.nextInt(gridLength - 2 * minDist);
            final BlockPos initialPos = new BlockPos(x, searchHeight.get(), z);
            final BlockPos alignedPos = getClosestGridPoint(initialPos);

            // Check minimum distance from other obstacles
            if (obstacles.stream().noneMatch(o -> o.getSquaredDistance(alignedPos) < spacing * spacing)) {
                obstacles.add(alignedPos);
            }
        }
    }

    private List<PathNode> findPathDijkstra(BlockPos start, BlockPos target) {
        PriorityQueue<DijkstraNode> queue = new PriorityQueue<>(Comparator.comparingDouble(n -> n.distance));
        Map<BlockPos, DijkstraNode> nodeMap = new HashMap<>();

        DijkstraNode startNode = new DijkstraNode(start, null, 0);
        queue.add(startNode);
        nodeMap.put(start, startNode);

        while (!queue.isEmpty()) {
            DijkstraNode current = queue.poll();

            if (current.pos.equals(target)) {
                return reconstructPath(current);
            }

            for (BlockPos neighbor : getValidNeighbors(current.pos)) {
                double newDist = current.distance + getMovementCost(current.pos, neighbor);
                DijkstraNode neighborNode = nodeMap.getOrDefault(neighbor, new DijkstraNode(neighbor, current, Double.POSITIVE_INFINITY));

                if (newDist < neighborNode.distance) {
                    neighborNode.parent = current;
                    neighborNode.distance = newDist;
                    nodeMap.put(neighbor, neighborNode);
                    queue.add(neighborNode);
                }
            }
        }

        return new ArrayList<>();
    }

    private List<PathNode> reconstructPath(DijkstraNode endNode) {
        List<PathNode> path = new ArrayList<>();
        DijkstraNode current = endNode;

        while (current != null) {
            Direction facing = current.parent != null ?
                calculateGridDirection(current.parent.pos, current.pos) :
                currentDirection;

            path.add(0, new PathNode(
                current.pos,
                facing,
                true,
                false,
                new ArrayList<>()
            ));
            current = current.parent;
        }

        return path;
    }

    private List<PathNode> findPathGreedy(BlockPos start, BlockPos target) {
        PriorityQueue<AStarNode> queue = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fScore));
        Set<BlockPos> visited = new HashSet<>();

        AStarNode startNode = new AStarNode(start, null);
        startNode.fScore = heuristic(start, target);
        queue.add(startNode);

        while (!queue.isEmpty()) {
            AStarNode current = queue.poll();

            if (current.pos.equals(target)) {
                return reconstructPath(current);
            }

            visited.add(current.pos);

            for (BlockPos neighbor : getValidNeighbors(current.pos)) {
                if (visited.contains(neighbor)) continue;

                AStarNode neighborNode = new AStarNode(neighbor, current);
                neighborNode.fScore = heuristic(neighbor, target);
                queue.add(neighborNode);
            }
        }

        return new ArrayList<>();
    }

    private List<PathNode> selectDynamicTargets(BlockPos playerPos) {
        // Find clusters of unvisited nodes
        List<List<PathNode>> clusters = findNodeClusters();

        // Select nearest cluster
        return clusters.stream()
            .min(Comparator.comparingDouble(cluster ->
                cluster.stream()
                    .mapToDouble(n -> playerPos.getSquaredDistance(n.pos()))
                    .min()
                    .orElse(Double.POSITIVE_INFINITY)
            ))
            .orElse(new ArrayList<>());
    }

    private List<List<PathNode>> findNodeClusters() {
        List<List<PathNode>> clusters = new ArrayList<>();
        Set<PathNode> unprocessed = gridNodes.stream()
            .filter(n -> !n.visited())
            .collect(Collectors.toSet());

        while (!unprocessed.isEmpty()) {
            List<PathNode> cluster = new ArrayList<>();
            Queue<PathNode> queue = new ArrayDeque<>();

            PathNode start = unprocessed.iterator().next();
            queue.add(start);
            unprocessed.remove(start);

            while (!queue.isEmpty()) {
                PathNode current = queue.poll();
                cluster.add(current);

                // Find nearby unprocessed nodes
                double clusterRadius = nodeSpacing.get() * 2;
                List<PathNode> nearby = unprocessed.stream()
                    .filter(n -> n.pos().getSquaredDistance(current.pos()) <= clusterRadius * clusterRadius)
                    .collect(Collectors.toList());

                for (PathNode node : nearby) {
                    queue.add(node);
                    unprocessed.remove(node);
                }
            }

            clusters.add(cluster);
        }

        return clusters;
    }

    private class DijkstraNode {
        BlockPos pos;
        DijkstraNode parent;
        double distance;

        DijkstraNode(BlockPos pos, DijkstraNode parent, double distance) {
            this.pos = pos;
            this.parent = parent;
            this.distance = distance;
        }
    }

    private List<PathNode> findPathJPS(BlockPos start, BlockPos target) {
        PriorityQueue<JPSNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fScore));
        Map<BlockPos, JPSNode> nodeMap = new HashMap<>();
        Set<BlockPos> closedSet = new HashSet<>();

        JPSNode startNode = new JPSNode(start, null);
        startNode.gScore = 0;
        startNode.fScore = heuristic(start, target);
        openSet.add(startNode);
        nodeMap.put(start, startNode);

        while (!openSet.isEmpty()) {
            JPSNode current = openSet.poll();

            if (current.pos.equals(target)) {
                return reconstructJPSPath(current);
            }

            closedSet.add(current.pos);

            // Find jump points in all allowed directions
            List<JPSNode> successors = findSuccessors(current, target);
            for (JPSNode successor : successors) {
                if (closedSet.contains(successor.pos)) continue;

                double tentativeG = current.gScore + getMovementCost(current.pos, successor.pos);
                JPSNode existingNode = nodeMap.get(successor.pos);

                if (existingNode == null || tentativeG < existingNode.gScore) {
                    successor.parent = current;
                    successor.gScore = tentativeG;
                    successor.fScore = tentativeG + heuristic(successor.pos, target);

                    nodeMap.put(successor.pos, successor);
                    openSet.add(successor);
                }
            }
        }

        return new ArrayList<>();
    }

    private List<JPSNode> findSuccessors(JPSNode node, BlockPos target) {
        List<JPSNode> successors = new ArrayList<>();

        // Check all possible directions
        for (Direction dir : Direction.values()) {
            BlockPos jumpPoint = findJumpPoint(node.pos, dir, target);
            if (jumpPoint != null) {
                successors.add(new JPSNode(jumpPoint, node));
            }
        }

        return successors;
    }

    private BlockPos findJumpPoint(BlockPos current, Direction dir, BlockPos target) {
        int spacing = nodeSpacing.get();
        BlockPos next = switch (dir) {
            case NORTH -> current.add(0, 0, -spacing);
            case SOUTH -> current.add(0, 0, spacing);
            case EAST -> current.add(spacing, 0, 0);
            case WEST -> current.add(-spacing, 0, 0);
        };

        if (!isValidPosition(next)) return null;
        if (next.equals(target)) return next;

        // Check for forced neighbors
        if (hasForcedNeighbor(next, dir)) {
            return next;
        }

        // Recursively look for jump points
        return findJumpPoint(next, dir, target);
    }

    private boolean hasForcedNeighbor(BlockPos pos, Direction dir) {
        int spacing = nodeSpacing.get();
        BlockPos[] neighbors = switch (dir) {
            case NORTH, SOUTH -> new BlockPos[]{
                pos.add(spacing, 0, 0),
                pos.add(-spacing, 0, 0)
            };
            case EAST, WEST -> new BlockPos[]{
                pos.add(0, 0, spacing),
                pos.add(0, 0, -spacing)
            };
        };

        // A forced neighbor exists if one side is blocked but its diagonal is free
        for (BlockPos neighbor : neighbors) {
            if (!isValidPosition(neighbor) && isValidPosition(neighbor.add(
                dir.getOffsetX() * spacing,
                0,
                dir.getOffsetZ() * spacing
            ))) {
                return true;
            }
        }

        return false;
    }

    private List<PathNode> reconstructJPSPath(JPSNode endNode) {
        List<BlockPos> rawPath = new ArrayList<>();
        JPSNode current = endNode;

        // Collect jump points
        while (current != null) {
            rawPath.add(0, current.pos);
            current = current.parent;
        }

        // Generate full path with intermediate points
        List<PathNode> fullPath = new ArrayList<>();
        for (int i = 0; i < rawPath.size() - 1; i++) {
            BlockPos start = rawPath.get(i);
            BlockPos end = rawPath.get(i + 1);

            // Add intermediate grid points
            List<BlockPos> segment = getGridPointsBetween(start, end);
            for (BlockPos point : segment) {
                Direction facing = calculateGridDirection(
                    i == 0 ? start : segment.get(segment.indexOf(point) - 1),
                    point
                );
                fullPath.add(new PathNode(point, facing, true, false, new ArrayList<>()));
            }
        }

        return fullPath;
    }

    private class JPSNode {
        BlockPos pos;
        JPSNode parent;
        double gScore = Double.POSITIVE_INFINITY;
        double fScore = Double.POSITIVE_INFINITY;

        JPSNode(BlockPos pos, JPSNode parent) {
            this.pos = pos;
            this.parent = parent;
        }
    }

    private void generatePerimeterChunks() {
        // Add perimeter chunks first
        for (int x = minChunkX; x <= maxChunkX; x++) {
            if (!obstacles.contains(new BlockPos(x * 16, searchHeight.get(), minChunkZ * 16))) {
                unexploredChunks.add(new ChunkPos(x, minChunkZ));
            }
            if (!obstacles.contains(new BlockPos(x * 16, searchHeight.get(), maxChunkZ * 16))) {
                unexploredChunks.add(new ChunkPos(x, maxChunkZ));
            }
        }
        for (int z = minChunkZ + 1; z < maxChunkZ; z++) {
            if (!obstacles.contains(new BlockPos(minChunkX * 16, searchHeight.get(), z * 16))) {
                unexploredChunks.add(new ChunkPos(minChunkX, z));
            }
            if (!obstacles.contains(new BlockPos(maxChunkX * 16, searchHeight.get(), z * 16))) {
                unexploredChunks.add(new ChunkPos(maxChunkX, z));
            }
        }
    }

    @EventHandler
    private void onTick(TickEvent.Post event) {
        try {
            if (mc.player == null || mc.world == null || !initialized) return;

            // Enhanced boundary checking every tick
            BlockPos playerPos = mc.player.getBlockPos();
            if (!isInGridBounds(playerPos)) {
                if (notifications.get()) {
                    showToast("Boundary Violation",
                        String.format("Outside grid at %s - stopping movement", playerPos.toShortString()),
                        Items.BARRIER);
                }

                // Immediately stop all movement
                if (flightMode.get() == FlightMode.BARITONE && baritone != null && baritoneActive) {
                    stopBaritone();
                } else if (flightMode.get() == FlightMode.VANILLA || flightMode.get() == FlightMode.PITCH40) {
                    // Flight systems will naturally guide back to target
                    // Just update the target to be within bounds
                    returnToGridWithFlight();
                }
                // No need to stop auto-walk in flight mode - AFKVanillaFly handles movement
                return;
            }

            maintainHeight();
            updateMovementStats();

            // Update statistics and progress reporting
            updateStats();

            // Periodic progress reporting (every 30 seconds)
            if (System.currentTimeMillis() % 30000 < 50) {
                reportProgress();
            }

            // Periodic performance logging (every 5 minutes)
            if (System.currentTimeMillis() % 300000 < 50) {
                logPerformanceStats();
            }

            // Periodic state saving to prevent data loss
            if (autoSaveInterval.get() > 0 &&
                System.currentTimeMillis() % (autoSaveInterval.get() * 1000L) < 50) {
                saveCurrentState();
            }

            // Handle command execution (skip if using Baritone)
            if (!useBaritone.get() && execState != CommandExecutionState.DONE) {
                handleCommandExecution();
                return;
            }

            // Process command queue if not executing (skip if using Baritone)
            if (!useBaritone.get() && !commandQueue.isEmpty() && mc.player.age - lastCommandTick >= MIN_COMMAND_INTERVAL) {
                String cmd = commandQueue.poll();
                sendCommand(cmd);
                lastCommandTick = mc.player.age;
                return;
            }

            // Decrement wait ticks (skip if using Baritone)
            if (!useBaritone.get() && waitTicks > 0) {
                waitTicks--;
                return;
            }

            // State machine and normal operation (skip if using Baritone)
            if (!useBaritone.get() && commandState != CommandState.IDLE) {
                try {
                    executeCommandSequence();
                } catch (Exception e) {
                    showToast("State Error", e.getMessage(), Items.BARRIER);
                    resetState();
                }
                return;
            }

            // Always use grid exploration
            handleGridExploration();
        } catch (Exception e) {
            // Prevent complete crashes - log error and continue
            showToast("Critical Error", "Tick error: " + e.getMessage(), Items.BARRIER);
            try {
                resetState();
            } catch (Exception resetError) {
                // If reset fails, disable module
                showToast("Fatal Error", "Module disabled due to critical error", Items.BARRIER);
                this.toggle();
            }
        }
    }

    private void handleCommandExecution() {
        try {
            if (execTimer > 0) {
                execTimer--;
                return;
            }

            switch (execState) {
                case WAITING -> {
                    mc.setScreen(new ChatScreen(""));
                    execState = CommandExecutionState.OPEN_CHAT;
                    execTimer = preCommandDelay.get();
                }
                case OPEN_CHAT -> {
                    currentTypedText = "";
                    execState = CommandExecutionState.TYPING;
                    execTimer = charDelay.get();
                }
                case TYPING -> {
                    if (currentTypedText.length() < pendingCommand.length()) {
                        currentTypedText = pendingCommand.substring(0, currentTypedText.length() + 1);
                        mc.setScreen(new ChatScreen(currentTypedText));
                        execTimer = charDelay.get();
                    } else {
                        execState = manualEnter.get() ? CommandExecutionState.DONE : CommandExecutionState.ENTER_DOWN;
                        execTimer = postCommandDelay.get();
                    }
                }
                case ENTER_DOWN -> {
                    if (mc.currentScreen instanceof ChatScreen chat) {
                        mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 1, 0);
                        chat.keyPressed(GLFW.GLFW_KEY_ENTER, 0, 0);
                        execState = CommandExecutionState.ENTER_UP;
                        execTimer = 2;
                    }
                }
                case ENTER_UP -> {
                    mc.keyboard.onKey(mc.getWindow().getHandle(), GLFW.GLFW_KEY_ENTER, 0, 0, 0);
                    execState = CommandExecutionState.DONE;
                    execTimer = postCommandDelay.get();
                    currentTypedText = "";
                    pendingCommand = "";
                }
            }
        } catch (Exception e) {
            showToast("Command Error", e.getMessage(), Items.BARRIER);
            execState = CommandExecutionState.DONE;
            currentTypedText = "";
            pendingCommand = "";
            mc.setScreen(null);
        }
    }

    private boolean isRedundantCommand(String cmd) {
        if (cmd.startsWith(".toggle auto-walk")) {
            boolean enabling = cmd.endsWith("on");
            return enabling == autoWalkEnabled;
        }
        if (cmd.startsWith(".rotation set")) {
            for (Direction dir : Direction.values()) {
                if (cmd.endsWith(dir.command) && dir == currentDirection) {
                    return true;
                }
            }
        }
        return false;
    }

    private void resetState() {
        if (notifications.get()) {
            if (notifications.get()) {
                showToast("System", "Resetting to safe state", Items.REDSTONE);
            }
        }
        // Clear command queue and states
        commandQueue.clear();
        commandState = CommandState.IDLE;
        commandTimer = 0;
        waitTicks = 0;
        shouldCheckRotation = true;

        // Reset command execution
        // Reset execution state
        execState = CommandExecutionState.DONE;
        execTimer = 0;
        pendingCommand = "";

        // Clear any open chat screen
        if (mc.currentScreen instanceof ChatScreen) {
            mc.setScreen(null);
        }

        // Update HUD with final stats if needed
        if (debugMessages.get() && mc.player != null) {
            showFinalStats();
        }
    }

    private void executeCommandSequence() {
        if (commandTimer > 0) {
            commandTimer--;
            return;
        }

        switch (commandState) {
            case WAITING -> {
                if (waitTicks > 0) {
                    waitTicks--;
                    return;
                }
                verifyRotation();
            }
            case STOPPING_WALK -> {
                showToast("Direction Change", "Stopping for direction change", Items.COMPASS);
                queueCommand(".toggle auto-walk off");
                commandState = CommandState.TURNING;
                commandTimer = 15; // Extra time to ensure complete stop
            }
            case TURNING -> {
                float currentYaw = (mc.player.getYaw() % 360 + 360) % 360;
                float targetYaw = pendingDirection.yaw;
                double difference = Math.abs(currentYaw - targetYaw);
                // Handle wrap-around
                if (difference > 180) {
                    difference = 360 - difference;
                }

                if (difference > 2) {
                    queueCommand(".rotation set " + pendingDirection.command);
                    commandTimer = Math.max(5, (int)(difference / 10)); // More time for larger rotations
                } else {
                    currentDirection = pendingDirection;
                    pendingDirection = null;
                    commandState = CommandState.STABILIZING;
                    commandTimer = 10;
                }
            }
            case STABILIZING -> {
                // Check both grid alignment and rotation stability
                BlockPos currentPos = mc.player.getBlockPos();
                if (!isAlignedWithGrid(currentPos)) {
                    alignWithGrid();
                    commandTimer = 5;
                    return;
                }

                // Verify rotation precision
                float currentYaw = (mc.player.getYaw() % 360 + 360) % 360;
                float targetYaw = currentDirection.yaw;
                double angleDiff = Math.abs(currentYaw - targetYaw);
                if (angleDiff > 180) angleDiff = 360 - angleDiff;

                if (angleDiff > 2) {
                    queueCommand(".rotation set " + currentDirection.command);
                    commandTimer = 5;
                    return;
                }

                // Check movement has fully stopped
                double speed = mc.player.getVelocity().horizontalLength();
                if (speed > 0.01) {
                    commandTimer = 5;
                    return;
                }

                commandState = CommandState.RESUMING_WALK;
                commandTimer = 5;
            }
            case RESUMING_WALK -> {
                queueCommand(".toggle auto-walk on");
                commandState = CommandState.WAITING;
                waitTicks = 25; // Extended wait for stabilization
                shouldCheckRotation = true; // Enable rotation verification
            }
        }
    }

    private void verifyRotation() {
        if (currentDirection == null) return;

        BlockPos currentPos = mc.player.getBlockPos();
        float currentYaw = (mc.player.getYaw() % 360 + 360) % 360;
        float targetYaw = currentDirection.yaw;
        double difference = Math.abs(currentYaw - targetYaw);

        // Handle wrap-around
        if (difference > 180) {
            difference = 360 - difference;
        }

        // Always check grid alignment first
        if (!isAlignedWithGrid(currentPos)) {
            alignWithGrid();
            return;
        }

        // Check rotation precision
        if (difference > 2) {
            queueCommand(".rotation set " + currentDirection.command);
            commandTimer = Math.max(5, (int)(difference / 10));
            return;
        }

        // Ensure player has stopped completely after rotation
        Vec3d velocity = mc.player.getVelocity();
        if (Math.abs(velocity.horizontalLength()) > 0.01) {
            commandTimer = 5;
            return;
        }

        shouldCheckRotation = false;
        commandState = CommandState.IDLE;
    }

    private boolean shouldBeWalking() {
        // Don't walk during rotation or alignment
        if (commandState != CommandState.IDLE) return false;

        // Don't walk if not aligned with grid
        if (!isAlignedWithGrid(mc.player.getBlockPos())) return false;

        // Don't walk near boundaries
        ChunkPos playerChunk = mc.player.getChunkPos();
        if (isVeryCloseToBoundary(playerChunk.x, playerChunk.z)) return false;

        // Only walk if we have a valid direction and target
        return currentDirection != null && (currentNode != null || currentTarget != null);
    }

    private boolean isInGridBounds(ChunkPos pos) {
        return pos.x >= minChunkX && pos.x <= maxChunkX &&
               pos.z >= minChunkZ && pos.z <= maxChunkZ;
    }

    /**
     * Enhanced boundary checking with block-level precision
     */
    private boolean isInGridBounds(BlockPos pos) {
        int blockMinX = minChunkX * 16;
        int blockMaxX = (maxChunkX + 1) * 16 - 1;
        int blockMinZ = minChunkZ * 16;
        int blockMaxZ = (maxChunkZ + 1) * 16 - 1;

        return pos.getX() >= blockMinX && pos.getX() <= blockMaxX &&
               pos.getZ() >= blockMinZ && pos.getZ() <= blockMaxZ;
    }

    /**
     * Check if position is near grid boundary (within safety margin)
     */
    private boolean isNearGridBoundary(BlockPos pos) {
        int safetyMargin = 8; // blocks from boundary
        int blockMinX = minChunkX * 16 + safetyMargin;
        int blockMaxX = (maxChunkX + 1) * 16 - 1 - safetyMargin;
        int blockMinZ = minChunkZ * 16 + safetyMargin;
        int blockMaxZ = (maxChunkZ + 1) * 16 - 1 - safetyMargin;

        return pos.getX() <= blockMinX || pos.getX() >= blockMaxX ||
               pos.getZ() <= blockMinZ || pos.getZ() >= blockMaxZ;
    }

    private void handleGridExploration() {
        // First ensure we're within grid bounds
        ChunkPos playerChunk = mc.player.getChunkPos();
        if (!isInGridBounds(playerChunk)) {
            if (notifications.get()) {
                showToast("Grid Status", "Outside bounds - returning to grid", Items.MAP);
            }

            if (useBaritone.get() && baritone != null) {
                returnToGridWithBaritone();
            } else {
                moveToNearestGridPoint();
            }
            return;
        }

        // Don't continue if commands are pending (unless using Baritone)
        if (!useBaritone.get() && (commandState != CommandState.IDLE || !commandQueue.isEmpty())) {
            return;
        }

        // Check if exploration is complete
        if (isExplorationComplete()) {
            stopBaritone();
            showToast("SearchBot", "Exploration completed!", Items.EMERALD);
            this.toggle();
            return;
        }

        // Use flight system or fallback navigation
        if (flightMode.get() == FlightMode.BARITONE && baritone != null) {
            handleBaritoneNavigation();
        } else if (flightMode.get() == FlightMode.VANILLA || flightMode.get() == FlightMode.PITCH40) {
            handleFlightNavigation();
        } else {
            updateNavigation();
        }
    }

    private void moveToNearestGridPoint() {
        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos nearestGridPoint = getClosestGridPoint(playerPos);

        if (!playerPos.equals(nearestGridPoint)) {
            Direction dirToGrid = calculateGridDirection(playerPos, nearestGridPoint);
            if (dirToGrid != currentDirection) {
                queueCommand(".rotation set " + dirToGrid.command);
                currentDirection = dirToGrid;
            }
            if (!autoWalkEnabled) {
                toggleAutoWalk(true);
            }
        }
    }

    /**
     * Handle navigation using TrailFollower-style flight system
     */
    private void handleFlightNavigation() {
        try {
            // Get current target node or find a new one
            if (currentNode == null || currentNode.visited()) {
                selectNextTargetNode();
            }

            if (currentNode == null) {
                showToast("SearchBot", "No more nodes to visit!", Items.EMERALD);
                this.toggle();
                return;
            }

            // Calculate target yaw for the current node (TrailFollower style)
            Vec3d playerPos = mc.player.getPos();
            Vec3d targetPos = Vec3d.ofCenter(currentNode.pos());
            targetYaw = Rotations.getYaw(targetPos.subtract(playerPos));

            // Apply TrailFollower's smooth rotation (YAWLOCK mode)
            float currentYaw = getActualYaw(mc.player.getYaw());
            float smoothedYaw = smoothRotation(currentYaw, (float) targetYaw);
            mc.player.setYaw(smoothedYaw);

            // Check if we've reached the current node
            double distanceToTarget = playerPos.distanceTo(targetPos);
            if (distanceToTarget <= nodeReachDistance.get()) {
                markCurrentNodeVisited();
                return;
            }

            // AFKVanillaFly handles forward movement automatically
            // No auto-walk direction changes needed - just fly forward and rotate camera

        } catch (Exception e) {
            showToast("Flight Navigation Error", e.getMessage(), Items.BARRIER);
            resetState();
        }
    }

    /**
     * TrailFollower's exact smooth rotation algorithm
     */
    private float smoothRotation(double current, double target) {
        double difference = angleDifference(target, current);
        return (float) (current + difference * rotateScaling.get());
    }

    /**
     * TrailFollower's angle difference calculation
     */
    private double angleDifference(double target, double current) {
        double diff = (target - current + 180) % 360 - 180;
        return diff < -180 ? diff + 360 : diff;
    }

    /**
     * TrailFollower's yaw normalization
     */
    private float getActualYaw(float yaw) {
        return (yaw % 360 + 360) % 360;
    }

    /**
     * Mark current node as visited (simplified for flight mode)
     */
    private void markCurrentNodeVisited() {
        try {
            if (currentNode != null && currentNodeIndex >= 0 && currentNodeIndex < gridNodes.size()) {
                // Mark current node as visited
                PathNode visitedNode = currentNode.withVisited(true);
                gridNodes.set(currentNodeIndex, visitedNode);

                if (notifications.get()) {
                    int visitedCount = (int) gridNodes.stream().filter(PathNode::visited).count();
                    showToast("Node Complete",
                        String.format("Reached %s (%d/%d visited)",
                            currentNode.pos().toShortString(), visitedCount, gridNodes.size()),
                        Items.EMERALD);
                }

                // Update persistent state
                updateExplorationState();
                saveCurrentState();

                // Select next target immediately
                selectNextTargetNode();
            }
        } catch (Exception e) {
            showToast("Node Completion Error", e.getMessage(), Items.BARRIER);
        }
    }

    /**
     * Handle navigation using Baritone pathfinding
     */
    private void handleBaritoneNavigation() {
        try {
            // Check if Baritone is currently pathfinding
            if (baritoneActive) {
                // Check if Baritone has reached the target or timed out
                if (hasBaritoneReachedTarget() || hasBaritoneTimedOut()) {
                    handleBaritoneCompletion();
                }
                return;
            }

            // Get current target node or find a new one
            if (currentNode == null || currentNode.visited()) {
                selectNextTargetNode();
            }

            if (currentNode == null) {
                showToast("SearchBot", "No more nodes to visit!", Items.EMERALD);
                this.toggle();
                return;
            }

            // Start Baritone pathfinding to the target node
            startBaritonePathfinding(currentNode.pos());

        } catch (Exception e) {
            showToast("Baritone Navigation Error", e.getMessage(), Items.BARRIER);
            resetState();
        }
    }

    /**
     * Start Baritone pathfinding to a target position
     */
    private void startBaritonePathfinding(BlockPos target) {
        try {
            if (baritone == null) {
                showToast("Baritone Error", "Baritone instance is null", Items.BARRIER);
                return;
            }

            // Stop any existing pathfinding
            baritone.getPathingBehavior().cancelEverything();

            // Create goal based on node reach distance
            Goal goal;
            double reachDistance = nodeReachDistance.get();
            if (reachDistance <= 1.0) {
                goal = new GoalBlock(target);
            } else {
                goal = new GoalNear(target, (int) Math.ceil(reachDistance));
            }

            // Start pathfinding
            baritone.getCustomGoalProcess().setGoalAndPath(goal);

            // Update state
            baritoneActive = true;
            baritoneStartTime = System.currentTimeMillis();
            baritoneTarget = target;
            currentGoal = goal;

            if (notifications.get()) {
                showToast("Baritone",
                    String.format("Pathfinding to %s", target.toShortString()),
                    Items.COMPASS);
            }

        } catch (Exception e) {
            showToast("Baritone Error", "Failed to start pathfinding: " + e.getMessage(), Items.BARRIER);
            baritoneActive = false;
        }
    }

    /**
     * Check if Baritone has reached the target
     */
    private boolean hasBaritoneReachedTarget() {
        if (baritoneTarget == null || mc.player == null) return false;

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = Vec3d.ofCenter(baritoneTarget);
        double distance = playerPos.distanceTo(targetPos);

        return distance <= nodeReachDistance.get();
    }

    /**
     * Check if Baritone pathfinding has timed out
     */
    private boolean hasBaritoneTimedOut() {
        if (!baritoneActive) return false;

        long elapsed = System.currentTimeMillis() - baritoneStartTime;
        return elapsed > (baritoneTimeout.get() * 1000L);
    }

    /**
     * Handle completion of Baritone pathfinding
     */
    private void handleBaritoneCompletion() {
        try {
            boolean reachedTarget = hasBaritoneReachedTarget();
            boolean timedOut = hasBaritoneTimedOut();

            if (reachedTarget) {
                // Mark current node as visited
                if (currentNode != null && currentNodeIndex >= 0 && currentNodeIndex < gridNodes.size()) {
                    PathNode visitedNode = currentNode.withVisited(true);
                    gridNodes.set(currentNodeIndex, visitedNode);

                    if (notifications.get()) {
                        int visitedCount = (int) gridNodes.stream().filter(PathNode::visited).count();
                        showToast("Node Complete",
                            String.format("Reached %s (%d/%d visited)",
                                currentNode.pos().toShortString(), visitedCount, gridNodes.size()),
                            Items.EMERALD);
                    }
                }

                // Update persistent state
                updateExplorationState();
                saveCurrentState();

            } else if (timedOut) {
                showToast("Baritone Timeout",
                    String.format("Failed to reach %s in %d seconds",
                        baritoneTarget.toShortString(), baritoneTimeout.get()),
                    Items.BARRIER);
            }

            // Stop Baritone and reset state
            stopBaritone();

        } catch (Exception e) {
            showToast("Baritone Completion Error", e.getMessage(), Items.BARRIER);
            stopBaritone();
        }
    }

    /**
     * Stop Baritone pathfinding
     */
    private void stopBaritone() {
        try {
            if (baritone != null && baritoneActive) {
                baritone.getPathingBehavior().cancelEverything();
            }
        } catch (Exception e) {
            showToast("Baritone Stop Error", e.getMessage(), Items.BARRIER);
        } finally {
            baritoneActive = false;
            baritoneTarget = null;
            currentGoal = null;
        }
    }

    /**
     * Return to grid using Baritone when outside boundaries
     */
    private void returnToGridWithBaritone() {
        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos nearestGridPoint = getClosestGridPoint(playerPos);

        // Ensure the grid point is within boundaries
        ChunkPos gridChunk = new ChunkPos(nearestGridPoint);
        if (!isInGridBounds(gridChunk)) {
            // Find the nearest point within grid boundaries
            int centerX = (minChunkX + maxChunkX) * 8; // Convert to block coordinates
            int centerZ = (minChunkZ + maxChunkZ) * 8;
            nearestGridPoint = new BlockPos(centerX, searchHeight.get(), centerZ);
        }

        startBaritonePathfinding(nearestGridPoint);
    }

    /**
     * Return to grid using flight system when outside boundaries
     */
    private void returnToGridWithFlight() {
        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos nearestGridPoint = getClosestGridPoint(playerPos);

        // Ensure the grid point is within boundaries
        ChunkPos gridChunk = new ChunkPos(nearestGridPoint);
        if (!isInGridBounds(gridChunk)) {
            // Find the nearest point within grid boundaries
            int centerX = (minChunkX + maxChunkX) * 8; // Convert to block coordinates
            int centerZ = (minChunkZ + maxChunkZ) * 8;
            nearestGridPoint = new BlockPos(centerX, searchHeight.get(), centerZ);
        }

        // Create a temporary node for the return point
        currentNode = new PathNode(nearestGridPoint, Direction.NORTH, false, false, new ArrayList<>());
        currentNodeIndex = -1; // Special index for return navigation

        if (notifications.get()) {
            showToast("Boundary Recovery",
                String.format("Flying back to grid at %s", nearestGridPoint.toShortString()),
                Items.ELYTRA);
        }
    }

    /**
     * Check if exploration is complete based on minimum nodes setting
     */
    private boolean isExplorationComplete() {
        int visitedCount = (int) gridNodes.stream().filter(PathNode::visited).count();
        int minNodes = minNodesToVisit.get();

        if (minNodes > 0) {
            return visitedCount >= minNodes;
        } else {
            // Visit all nodes if minimum is 0
            return visitedCount >= gridNodes.size();
        }
    }

    /**
     * Select the next target node based on visit order and priority
     */
    private void selectNextTargetNode() {
        try {
            BlockPos playerPos = mc.player.getBlockPos();

            // Find unvisited nodes
            List<PathNode> unvisitedNodes = gridNodes.stream()
                .filter(n -> !n.visited())
                .collect(Collectors.toList());

            if (unvisitedNodes.isEmpty()) {
                currentNode = null;
                return;
            }

            // Select based on visit order
            PathNode selectedNode = switch(visitOrder.get()) {
                case NEAREST -> findNearestNode(unvisitedNodes, playerPos);
                case SPIRAL -> findNextSpiralNode(unvisitedNodes, playerPos);
                case SNAKE -> findNextSnakeNode(unvisitedNodes);
                case QUADRANT -> findNextQuadrantNode(unvisitedNodes, playerPos);
                case PERIMETER -> findNextPerimeterNode(unvisitedNodes);
            };

            if (selectedNode != null) {
                currentNode = selectedNode;
                currentNodeIndex = gridNodes.indexOf(selectedNode);

                if (notifications.get()) {
                    int visitedCount = (int) gridNodes.stream().filter(PathNode::visited).count();
                    showToast("New Target",
                        String.format("Targeting %s (%d/%d visited)",
                            selectedNode.pos().toShortString(), visitedCount, gridNodes.size()),
                        Items.TARGET);
                }
            } else {
                if (notifications.get() && debugMessages.get()) {
                    showToast("Node Selection", "No valid node found for visit order: " + visitOrder.get(), Items.BARRIER);
                }
            }

        } catch (Exception e) {
            showToast("Node Selection Error", e.getMessage(), Items.BARRIER);
            currentNode = null;
        }
    }

    /**
     * Navigate to the current target node
     */
    private void navigateToCurrentNode() {
        if (currentNode == null) return;

        try {
            BlockPos playerPos = mc.player.getBlockPos();
            BlockPos targetPos = currentNode.pos();
            Vec3d playerVec = mc.player.getPos();
            Vec3d targetVec = Vec3d.ofCenter(targetPos);

            double distanceToTarget = playerVec.distanceTo(targetVec);

            // Check if we've reached the node
            if (distanceToTarget <= nodeReachDistance.get()) {
                handleNodeReached();
                return;
            }

            // Calculate direction to target
            Direction targetDirection = calculateGridDirection(playerPos, targetPos);

            // Use the existing turn sequence system for direction changes
            if (targetDirection != currentDirection) {
                if (autoWalkEnabled) {
                    toggleAutoWalk(false);
                }
                startTurnSequence(targetDirection);
                return;
            }

            // Check if rotation is aligned
            if (!isRotationAligned(currentDirection)) {
                queueCommand(".rotation set " + currentDirection.command);
                return;
            }

            // Start moving if we're properly aligned and not already moving
            if (!autoWalkEnabled) {
                toggleAutoWalk(true);
                if (notifications.get() && debugMessages.get()) {
                    showToast("Movement", "Starting movement to " + targetPos.toShortString(), Items.DIAMOND_BOOTS);
                }
            }

            // Show progress if enabled
            if (showNodeProgress.get() && System.currentTimeMillis() % 5000 < 50) {
                showToast("Navigation",
                    String.format("Distance to target: %.1f blocks", distanceToTarget),
                    Items.COMPASS);
            }

        } catch (Exception e) {
            showToast("Navigation Error", e.getMessage(), Items.BARRIER);
            resetState();
        }
    }

    /**
     * Node selection methods for different visit orders
     */
    private PathNode findNearestNode(List<PathNode> nodes, BlockPos playerPos) {
        return nodes.stream()
            .min(Comparator.comparingDouble(n -> playerPos.getSquaredDistance(n.pos())))
            .orElse(null);
    }

    private PathNode findNextSpiralNode(List<PathNode> nodes, BlockPos playerPos) {
        // Find center of grid
        BlockPos center = new BlockPos(
            (minChunkX + maxChunkX) * 8,
            searchHeight.get(),
            (minChunkZ + maxChunkZ) * 8
        );

        // Sort by distance from center, then by angle for spiral pattern
        return nodes.stream()
            .min((a, b) -> {
                double distA = center.getSquaredDistance(a.pos());
                double distB = center.getSquaredDistance(b.pos());

                if (Math.abs(distA - distB) < 100) {
                    double angleA = Math.atan2(a.pos().getZ() - center.getZ(), a.pos().getX() - center.getX());
                    double angleB = Math.atan2(b.pos().getZ() - center.getZ(), b.pos().getX() - center.getX());
                    return Double.compare(angleA, angleB);
                }

                return Double.compare(distA, distB);
            })
            .orElse(null);
    }

    private PathNode findNextSnakeNode(List<PathNode> nodes) {
        // Sort by Z coordinate first (rows), then by X coordinate
        return nodes.stream()
            .min((a, b) -> {
                int zCompare = Integer.compare(a.pos().getZ(), b.pos().getZ());
                if (zCompare != 0) return zCompare;

                // For even rows, go left to right; for odd rows, go right to left
                int row = (a.pos().getZ() - minChunkZ * 16) / nodeSpacing.get();
                if (row % 2 == 0) {
                    return Integer.compare(a.pos().getX(), b.pos().getX());
                } else {
                    return Integer.compare(b.pos().getX(), a.pos().getX());
                }
            })
            .orElse(null);
    }

    private PathNode findNextQuadrantNode(List<PathNode> nodes, BlockPos playerPos) {
        // Simple quadrant-based selection - find nearest in current quadrant first
        BlockPos center = new BlockPos(
            (minChunkX + maxChunkX) * 8,
            searchHeight.get(),
            (minChunkZ + maxChunkZ) * 8
        );

        // Determine current quadrant
        boolean playerEast = playerPos.getX() >= center.getX();
        boolean playerSouth = playerPos.getZ() >= center.getZ();

        // Find nodes in same quadrant first
        List<PathNode> sameQuadrant = nodes.stream()
            .filter(n -> {
                boolean nodeEast = n.pos().getX() >= center.getX();
                boolean nodeSouth = n.pos().getZ() >= center.getZ();
                return nodeEast == playerEast && nodeSouth == playerSouth;
            })
            .collect(Collectors.toList());

        if (!sameQuadrant.isEmpty()) {
            return findNearestNode(sameQuadrant, playerPos);
        }

        // If no nodes in same quadrant, find nearest overall
        return findNearestNode(nodes, playerPos);
    }

    private PathNode findNextPerimeterNode(List<PathNode> nodes) {
        // Find nodes on the perimeter first
        List<PathNode> perimeterNodes = nodes.stream()
            .filter(n -> {
                BlockPos pos = n.pos();
                int chunkX = pos.getX() / 16;
                int chunkZ = pos.getZ() / 16;
                return chunkX == minChunkX || chunkX == maxChunkX ||
                       chunkZ == minChunkZ || chunkZ == maxChunkZ;
            })
            .collect(Collectors.toList());

        if (!perimeterNodes.isEmpty()) {
            // Sort perimeter nodes by position for systematic coverage
            return perimeterNodes.stream()
                .min(Comparator.comparingInt((PathNode n) -> n.pos().getX())
                    .thenComparingInt(n -> n.pos().getZ()))
                .orElse(null);
        }

        // If no perimeter nodes, find any node
        return nodes.stream().findFirst().orElse(null);
    }

    /**
     * Initialize flight systems based on selected mode
     */
    private void initializeFlightSystems() {
        try {
            vanillaFly = Modules.get().get(AFKVanillaFly.class);

            switch (flightMode.get()) {
                case VANILLA, PITCH40 -> {
                    // Use TrailFollower's AFKVanillaFly activation approach
                    if (vanillaFly != null && !vanillaFly.isActive()) {
                        // TrailFollower's triple-toggle approach for reliability
                        vanillaFly.toggle();
                        mc.execute(() -> {
                            vanillaFly.toggle();
                            mc.execute(() -> {
                                vanillaFly.toggle();
                            });
                        });
                        flightSystemActive = true;
                        showToast("Flight System",
                            String.format("AFKVanillaFly activated (%s mode)", flightMode.get().name()),
                            Items.ELYTRA);
                    }
                }
            }
        } catch (Exception e) {
            showToast("Flight Error", "Failed to initialize flight system: " + e.getMessage(), Items.BARRIER);
        }
    }

    /**
     * Stop all flight systems
     */
    private void stopFlightSystems() {
        try {
            if (vanillaFly != null && vanillaFly.isActive()) {
                vanillaFly.toggle();
            }
            flightSystemActive = false;
        } catch (Exception e) {
            showToast("Flight Error", "Error stopping flight systems: " + e.getMessage(), Items.BARRIER);
        }
    }

    private void handlePerimeterMovement() {
        ChunkPos playerChunk = mc.player.getChunkPos();
        BlockPos pos = mc.player.getBlockPos();

        // Don't process if commands are pending
        if (commandState != CommandState.IDLE || !commandQueue.isEmpty()) {
            return;
        }

        // Always maintain grid alignment
        if (!isAlignedWithGrid(pos)) {
            if (autoWalkEnabled) {
                toggleAutoWalk(false);
            }
            alignWithGrid();
            return;
        }

        // Get current perimeter segment info
        PerimeterSegment segment = getCurrentSegment(playerChunk);
        if (segment == null) {
            showToast("Navigation", "Returning to perimeter", Items.DIAMOND_BOOTS);
            moveToNearestPerimeterPoint();
            return;
        }

        // Check if we need to turn
        Direction targetDirection = segment.getDirection();
        boolean approachingCorner = segment.isApproachingCorner(playerChunk);
        Direction cornerTurn = approachingCorner ? segment.getNextDirection() : null;

        // Handle turns
        if (approachingCorner && cornerTurn != currentDirection) {
            toggleAutoWalk(false);
            startTurnSequence(cornerTurn);
            return;
        } else if (targetDirection != currentDirection) {
            toggleAutoWalk(false);
            startTurnSequence(targetDirection);
            return;
        }

        // Verify rotation is precise
        if (!isRotationAligned(currentDirection)) {
            queueCommand(".rotation set " + currentDirection.command);
            return;
        }

        // Continue movement if properly aligned
        if (!autoWalkEnabled) {
            toggleAutoWalk(true);
        }
    }

    private enum PerimeterSide {
        TOP(Direction.SOUTH),
        RIGHT(Direction.WEST),
        BOTTOM(Direction.NORTH),
        LEFT(Direction.EAST);

        final Direction nextDirection;
        PerimeterSide(Direction next) {
            this.nextDirection = next;
        }
    }

    private record PerimeterSegment(
        PerimeterSide side,
        int coordinate,
        Direction direction,
        int minX,
        int maxX,
        int minZ,
        int maxZ
    ) {
        public Direction getDirection() {
            return direction;
        }

        public Direction getNextDirection() {
            return side.nextDirection;
        }

        public boolean isApproachingCorner(ChunkPos pos) {
            return switch (side) {
                case TOP -> pos.x >= maxX - 1;
                case RIGHT -> pos.z >= maxZ - 1;
                case BOTTOM -> pos.x <= minX + 1;
                case LEFT -> pos.z <= minZ + 1;
            };
        }
    }

    private PerimeterSegment getCurrentSegment(ChunkPos pos) {
        if (pos.x == maxChunkX) {
            return new PerimeterSegment(PerimeterSide.TOP, maxChunkX, Direction.SOUTH,
                minChunkX, maxChunkX, minChunkZ, maxChunkZ);
        } else if (pos.z == maxChunkZ) {
            return new PerimeterSegment(PerimeterSide.RIGHT, maxChunkZ, Direction.WEST,
                minChunkX, maxChunkX, minChunkZ, maxChunkZ);
        } else if (pos.x == minChunkX) {
            return new PerimeterSegment(PerimeterSide.BOTTOM, minChunkX, Direction.NORTH,
                minChunkX, maxChunkX, minChunkZ, maxChunkZ);
        } else if (pos.z == minChunkZ) {
            return new PerimeterSegment(PerimeterSide.LEFT, minChunkZ, Direction.EAST,
                minChunkX, maxChunkX, minChunkZ, maxChunkZ);
        }
        return null;
    }

    private void moveToNearestPerimeterPoint() {
        ChunkPos playerChunk = mc.player.getChunkPos();
        ChunkPos target;

        // Find closest perimeter point
        if (Math.abs(playerChunk.x - minChunkX) <= Math.abs(playerChunk.x - maxChunkX)) {
            target = new ChunkPos(minChunkX, playerChunk.z);
            currentDirection = Direction.WEST;
        } else {
            target = new ChunkPos(maxChunkX, playerChunk.z);
            currentDirection = Direction.EAST;
        }

        if (Math.abs(playerChunk.z - minChunkZ) <= Math.abs(playerChunk.z - maxChunkZ)) {
            ChunkPos altTarget = new ChunkPos(playerChunk.x, minChunkZ);
            if (getChunkDistance(playerChunk, altTarget) < getChunkDistance(playerChunk, target)) {
                target = altTarget;
                currentDirection = Direction.NORTH;
            }
        } else {
            ChunkPos altTarget = new ChunkPos(playerChunk.x, maxChunkZ);
            if (getChunkDistance(playerChunk, altTarget) < getChunkDistance(playerChunk, target)) {
                target = altTarget;
                currentDirection = Direction.SOUTH;
            }
        }

        // Move towards target
        queueCommand(".rotation set " + currentDirection.command);
        if (!autoWalkEnabled) {
            toggleAutoWalk(true);
        }
    }

    private double calculateTrailCurvature() {
        if (trail.size() < 3) return 0;

        Vec3d[] points = trail.toArray(new Vec3d[0]);
        Vec3d p1 = points[0];
        Vec3d p2 = points[1];
        Vec3d p3 = points[2];

        Vec3d v1 = p2.subtract(p1);
        Vec3d v2 = p3.subtract(p2);

        // Calculate angle between vectors
        double dot = v1.normalize().dotProduct(v2.normalize());
        return Math.acos(Math.max(-1.0, Math.min(dot, 1.0)));
    }

    private void findNextUnexploredNode() {
        try {
            BlockPos playerPos = mc.player.getBlockPos();
            // Find nearest unvisited node within 100 blocks
            PathNode nearest = gridNodes.stream()
                .filter(n -> !n.visited() && n.pos().getSquaredDistance(playerPos) <= 10000)
                .min((a, b) -> Double.compare(
                    playerPos.getSquaredDistance(a.pos()),
                    playerPos.getSquaredDistance(b.pos())
                ))
                .orElse(null);

            if (nearest != null) {
                currentNode = nearest;
                currentNodeIndex = gridNodes.indexOf(nearest);
                return;
            }

            // If no nearby nodes, find any unvisited node
            for (int i = 0; i < gridNodes.size(); i++) {
                if (!gridNodes.get(i).visited()) {
                    currentNode = gridNodes.get(i);
                    currentNodeIndex = i;
                    return;
                }
            }

            // All nodes visited
            if (notifications.get()) {
                showToast("SearchBot", "All nodes explored!", Items.EMERALD);
            }
            currentPhase = ExplorationPhase.COMPLETED;
        } catch (Exception e) {
            if (notifications.get()) {
                showToast("Error", "Failed finding next node: " + e.getMessage(), Items.BARRIER);
            }
            resetState();
        }
    }

    private void maintainHeight() {
        double targetY = searchHeight.get();
        if (Math.abs(mc.player.getY() - targetY) > 5) {
            double verticalVelocity = mc.player.getY() < targetY ? 0.2 : -0.1;
            mc.player.setVelocity(mc.player.getVelocity().x, verticalVelocity, mc.player.getVelocity().z);
        }
    }

    private void generateGridNodes() {
        gridNodes.clear();
        int spacing = nodeSpacing.get();

        // Calculate grid dimensions in nodes
        int nodesX = (maxChunkX - minChunkX) * 16 / spacing;
        int nodesZ = (maxChunkZ - minChunkZ) * 16 / spacing;

        // Use improved node generation with terrain awareness
        List<PathNode> generatedNodes = generateOptimizedNodes(nodesX, nodesZ, spacing);

        // Create intelligent connections between nodes
        createNodeConnections(generatedNodes, spacing);

        gridNodes.addAll(generatedNodes);
        if (notifications.get()) {
            showToast("Grid Generation", String.format("Generated %d optimized nodes", gridNodes.size()), Items.MAP);
        }
    }

    /**
     * Generates nodes with terrain awareness and obstacle avoidance
     */
    private List<PathNode> generateOptimizedNodes(int nodesX, int nodesZ, int spacing) {
        List<PathNode> nodes = new ArrayList<>();
        int startX = minChunkX * 16;
        int startZ = minChunkZ * 16;

        // Generate base grid positions
        Set<BlockPos> validPositions = new HashSet<>();
        for (int x = 0; x < nodesX; x++) {
            for (int z = 0; z < nodesZ; z++) {
                BlockPos pos = new BlockPos(
                    startX + x * spacing,
                    searchHeight.get(),
                    startZ + z * spacing
                );

                // Only add positions that are valid and not obstructed
                if (isValidNodePosition(pos)) {
                    validPositions.add(pos);
                }
            }
        }

        // Convert to ordered list based on visit pattern
        List<BlockPos> orderedPositions = orderNodesByPattern(validPositions);

        // Create PathNode objects with optimized facing directions
        for (int i = 0; i < orderedPositions.size(); i++) {
            BlockPos pos = orderedPositions.get(i);
            Direction facing = calculateOptimalFacing(pos, orderedPositions, i);

            nodes.add(new PathNode(pos, facing, true, false, new ArrayList<>()));
        }

        return nodes;
    }

    /**
     * Creates intelligent connections between nodes based on accessibility
     */
    private void createNodeConnections(List<PathNode> nodes, int spacing) {
        for (int i = 0; i < nodes.size(); i++) {
            PathNode current = nodes.get(i);
            List<PathNode> connections = new ArrayList<>();

            // Find all reachable neighbors within connection range
            double maxConnectionDistance = spacing * 1.5; // Allow some flexibility

            for (int j = 0; j < nodes.size(); j++) {
                if (i == j) continue;

                PathNode candidate = nodes.get(j);
                double distance = current.pos().getSquaredDistance(candidate.pos());

                if (distance <= maxConnectionDistance * maxConnectionDistance) {
                    // Verify path is clear and follows grid rules
                    if (isValidConnection(current.pos(), candidate.pos(), spacing)) {
                        connections.add(candidate);
                    }
                }
            }

            // Update node with connections
            nodes.set(i, new PathNode(
                current.pos(),
                current.facing(),
                current.requiresWalk(),
                current.visited(),
                connections
            ));
        }
    }

    /**
     * Validates if a position is suitable for a node
     */
    private boolean isValidNodePosition(BlockPos pos) {
        // Check basic validity
        if (!isValidPosition(pos)) return false;

        // Check minimum distance from other obstacles
        int minDist = wallDistance.get();
        for (BlockPos obstacle : obstacles) {
            if (obstacle.getSquaredDistance(pos) < minDist * minDist) {
                return false;
            }
        }

        // Additional terrain checks could be added here
        // For example, checking for lava, void, or other hazards

        return true;
    }

    /**
     * Orders node positions based on the selected visit pattern
     */
    private List<BlockPos> orderNodesByPattern(Set<BlockPos> positions) {
        List<BlockPos> ordered = new ArrayList<>(positions);

        switch (visitOrder.get()) {
            case SPIRAL -> ordered = orderSpiral(ordered);
            case SNAKE -> ordered = orderSnake(ordered);
            case QUADRANT -> ordered = orderQuadrant(ordered);
            case PERIMETER -> ordered = orderPerimeter(ordered);
            case NEAREST -> ordered = orderNearest(ordered);
        }

        return ordered;
    }

    /**
     * Calculates optimal facing direction for a node based on its position in the sequence
     */
    private Direction calculateOptimalFacing(BlockPos pos, List<BlockPos> sequence, int index) {
        // Look ahead to next position if available
        if (index < sequence.size() - 1) {
            BlockPos next = sequence.get(index + 1);
            return calculateGridDirection(pos, next);
        }

        // Look back to previous position
        if (index > 0) {
            BlockPos prev = sequence.get(index - 1);
            return calculateGridDirection(prev, pos);
        }

        // Default to player direction
        BlockPos playerPos = mc.player.getBlockPos();
        Vec3d directionVec = Vec3d.ofCenter(pos).subtract(Vec3d.ofCenter(playerPos)).normalize();
        return getClosestDirection(Rotations.getYaw(directionVec));
    }

    /**
     * Validates if two nodes can be connected directly
     */
    private boolean isValidConnection(BlockPos from, BlockPos to, int spacing) {
        // Check if connection follows grid rules (cardinal or diagonal)
        int dx = Math.abs(to.getX() - from.getX());
        int dz = Math.abs(to.getZ() - from.getZ());

        // Must be along grid lines
        if (dx % spacing != 0 || dz % spacing != 0) return false;

        // Check if path is clear of obstacles
        List<BlockPos> pathPoints = getGridPointsBetween(from, to);
        for (BlockPos point : pathPoints) {
            if (!isValidPosition(point)) return false;
        }

        return true;
    }

    /**
     * Improved ordering methods for different visit patterns
     */
    private List<BlockPos> orderSpiral(List<BlockPos> positions) {
        if (positions.isEmpty()) return positions;

        // Find center point
        BlockPos center = findCenterPoint(positions);

        // Sort by spiral distance from center
        positions.sort((a, b) -> {
            double distA = center.getSquaredDistance(a);
            double distB = center.getSquaredDistance(b);

            // If distances are similar, use spiral angle
            if (Math.abs(distA - distB) < 100) {
                double angleA = Math.atan2(a.getZ() - center.getZ(), a.getX() - center.getX());
                double angleB = Math.atan2(b.getZ() - center.getZ(), b.getX() - center.getX());
                return Double.compare(angleA, angleB);
            }

            return Double.compare(distA, distB);
        });

        return positions;
    }

    private List<BlockPos> orderSnake(List<BlockPos> positions) {
        if (positions.isEmpty()) return positions;

        // Group by Z coordinate (rows)
        Map<Integer, List<BlockPos>> rows = positions.stream()
            .collect(Collectors.groupingBy(BlockPos::getZ));

        List<BlockPos> ordered = new ArrayList<>();
        List<Integer> sortedZ = rows.keySet().stream().sorted().collect(Collectors.toList());

        for (int i = 0; i < sortedZ.size(); i++) {
            List<BlockPos> row = rows.get(sortedZ.get(i));

            // Sort by X coordinate
            row.sort(Comparator.comparingInt(BlockPos::getX));

            // Reverse every other row for snake pattern
            if (i % 2 == 1) {
                Collections.reverse(row);
            }

            ordered.addAll(row);
        }

        return ordered;
    }

    private List<BlockPos> orderQuadrant(List<BlockPos> positions) {
        if (positions.isEmpty()) return positions;

        BlockPos center = findCenterPoint(positions);
        List<BlockPos> ordered = new ArrayList<>();

        // Define quadrants: NE, SE, SW, NW
        List<List<BlockPos>> quadrants = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            quadrants.add(new ArrayList<>());
        }

        // Distribute positions into quadrants
        for (BlockPos pos : positions) {
            int quadrant = 0;
            if (pos.getX() >= center.getX() && pos.getZ() <= center.getZ()) quadrant = 0; // NE
            else if (pos.getX() >= center.getX() && pos.getZ() > center.getZ()) quadrant = 1; // SE
            else if (pos.getX() < center.getX() && pos.getZ() > center.getZ()) quadrant = 2; // SW
            else quadrant = 3; // NW

            quadrants.get(quadrant).add(pos);
        }

        // Process each quadrant
        for (List<BlockPos> quadrant : quadrants) {
            quadrant.sort((a, b) -> Double.compare(
                center.getSquaredDistance(a),
                center.getSquaredDistance(b)
            ));
            ordered.addAll(quadrant);
        }

        return ordered;
    }

    private List<BlockPos> orderPerimeter(List<BlockPos> positions) {
        if (positions.isEmpty()) return positions;

        List<BlockPos> ordered = new ArrayList<>();
        Set<BlockPos> remaining = new HashSet<>(positions);

        // Find bounds
        int minX = positions.stream().mapToInt(BlockPos::getX).min().orElse(0);
        int maxX = positions.stream().mapToInt(BlockPos::getX).max().orElse(0);
        int minZ = positions.stream().mapToInt(BlockPos::getZ).min().orElse(0);
        int maxZ = positions.stream().mapToInt(BlockPos::getZ).max().orElse(0);

        // Process perimeter layers
        while (!remaining.isEmpty()) {
            List<BlockPos> perimeter = new ArrayList<>();

            // Collect current perimeter
            for (BlockPos pos : remaining) {
                if (pos.getX() == minX || pos.getX() == maxX ||
                    pos.getZ() == minZ || pos.getZ() == maxZ) {
                    perimeter.add(pos);
                }
            }

            if (perimeter.isEmpty()) {
                // Add remaining positions
                ordered.addAll(remaining);
                break;
            }

            // Sort perimeter clockwise starting from top-left
            perimeter.sort(this::comparePerimeterPositions);
            ordered.addAll(perimeter);
            remaining.removeAll(perimeter);

            // Shrink bounds for next layer
            minX++; maxX--; minZ++; maxZ--;
        }

        return ordered;
    }

    private List<BlockPos> orderNearest(List<BlockPos> positions) {
        if (positions.isEmpty()) return positions;

        List<BlockPos> ordered = new ArrayList<>();
        Set<BlockPos> remaining = new HashSet<>(positions);

        // Start from player position or center
        BlockPos current = mc.player != null ?
            getClosestGridPoint(mc.player.getBlockPos()) :
            findCenterPoint(positions);

        // Find nearest position to start
        final BlockPos startPos = current; // Make effectively final copy
        BlockPos start = remaining.stream()
            .min((a, b) -> Double.compare(
                startPos.getSquaredDistance(a),
                startPos.getSquaredDistance(b)
            ))
            .orElse(positions.get(0));

        ordered.add(start);
        remaining.remove(start);
        current = start;

        // Greedily select nearest remaining position
        while (!remaining.isEmpty()) {
            final BlockPos currentPos = current; // Make effectively final copy
            BlockPos nearest = remaining.stream()
                .min((a, b) -> Double.compare(
                    currentPos.getSquaredDistance(a),
                    currentPos.getSquaredDistance(b)
                ))
                .orElse(null);

            if (nearest != null) {
                ordered.add(nearest);
                remaining.remove(nearest);
                current = nearest;
            } else {
                break;
            }
        }

        return ordered;
    }

    private BlockPos findCenterPoint(List<BlockPos> positions) {
        if (positions.isEmpty()) return BlockPos.ORIGIN;

        int sumX = positions.stream().mapToInt(BlockPos::getX).sum();
        int sumZ = positions.stream().mapToInt(BlockPos::getZ).sum();

        return new BlockPos(
            sumX / positions.size(),
            searchHeight.get(),
            sumZ / positions.size()
        );
    }

    private int comparePerimeterPositions(BlockPos a, BlockPos b) {
        // Sort clockwise starting from top-left corner
        // Priority: top edge (left to right), right edge (top to bottom),
        // bottom edge (right to left), left edge (bottom to top)

        int minX = Math.min(a.getX(), b.getX());
        int maxX = Math.max(a.getX(), b.getX());
        int minZ = Math.min(a.getZ(), b.getZ());
        int maxZ = Math.max(a.getZ(), b.getZ());

        // Determine edge for each position
        int edgeA = getPerimeterEdge(a, minX, maxX, minZ, maxZ);
        int edgeB = getPerimeterEdge(b, minX, maxX, minZ, maxZ);

        if (edgeA != edgeB) {
            return Integer.compare(edgeA, edgeB);
        }

        // Same edge, sort by position along edge
        return switch (edgeA) {
            case 0 -> Integer.compare(a.getX(), b.getX()); // Top edge
            case 1 -> Integer.compare(a.getZ(), b.getZ()); // Right edge
            case 2 -> Integer.compare(b.getX(), a.getX()); // Bottom edge
            case 3 -> Integer.compare(b.getZ(), a.getZ()); // Left edge
            default -> 0;
        };
    }

    private int getPerimeterEdge(BlockPos pos, int minX, int maxX, int minZ, int maxZ) {
        if (pos.getZ() == minZ) return 0; // Top edge
        if (pos.getX() == maxX) return 1; // Right edge
        if (pos.getZ() == maxZ) return 2; // Bottom edge
        if (pos.getX() == minX) return 3; // Left edge
        return 0; // Default to top
    }

    private List<PathNode> generateSpiralNodes(int nodesX, int nodesZ, int spacing) {
        List<PathNode> nodes = new ArrayList<>();
        int startX = minChunkX * 16;
        int startZ = minChunkZ * 16;
        int centerX = startX + (maxChunkX - minChunkX) * 8;
        int centerZ = startZ + (maxChunkZ - minChunkZ) * 8;

        int x = 0, z = 0;
        int dx = 0, dz = -1;
        int maxSteps = Math.max(nodesX, nodesZ) * Math.max(nodesX, nodesZ);

        for (int i = 0; i < maxSteps; i++) {
            if ((-nodesX/2 <= x && x <= nodesX/2) && (-nodesZ/2 <= z && z <= nodesZ/2)) {
                BlockPos pos = new BlockPos(
                    centerX + x * spacing,
                    searchHeight.get(),
                    centerZ + z * spacing
                );
                // Determine facing direction based on movement
                Direction facing;
                if (dx == 1) facing = Direction.EAST;
                else if (dx == -1) facing = Direction.WEST;
                else if (dz == 1) facing = Direction.SOUTH;
                else facing = Direction.NORTH;

                nodes.add(new PathNode(pos, facing, true, false, new ArrayList<>()));
            }

            // Spiral pattern logic
            if (x == z || (x < 0 && x == -z) || (x > 0 && x == 1-z)) {
                int temp = dx;
                dx = -dz;
                dz = temp;
            }
            x += dx;
            z += dz;
        }

        return nodes;
    }

    private List<PathNode> generateSnakeNodes(int nodesX, int nodesZ, int spacing) {
        List<PathNode> nodes = new ArrayList<>();
        int startX = minChunkX * 16;
        int startZ = minChunkZ * 16;

        for (int z = 0; z < nodesZ; z++) {
            boolean rightToLeft = (z % 2 == 0);
            for (int x = 0; x < nodesX; x++) {
                int actualX = rightToLeft ? x : (nodesX - 1 - x);
                BlockPos pos = new BlockPos(
                    startX + actualX * spacing,
                    searchHeight.get(),
                    startZ + z * spacing
                );
                // Set facing direction based on movement pattern
                Direction facing;
                if (rightToLeft) {
                    facing = (x == nodesX - 1) ? Direction.SOUTH : Direction.EAST;
                } else {
                    facing = (x == 0) ? Direction.SOUTH : Direction.WEST;
                }
                nodes.add(new PathNode(pos, facing, true, false, new ArrayList<>()));
            }
        }
        return nodes;
    }

    private List<PathNode> generateQuadrantNodes(int nodesX, int nodesZ, int spacing) {
        List<PathNode> nodes = new ArrayList<>();
        int centerX = (minChunkX * 16 + maxChunkX * 16) / 2;
        int centerZ = (minChunkZ * 16 + maxChunkZ * 16) / 2;
        int halfX = nodesX / 2;
        int halfZ = nodesZ / 2;

        // Generate in quadrant order: NE, SE, SW, NW
        int[][] quadrants = {{1,1}, {1,-1}, {-1,-1}, {-1,1}};

        for (int[] quadrant : quadrants) {
            for (int dx = 0; dx <= halfX; dx++) {
                for (int dz = 0; dz <= halfZ; dz++) {
                    BlockPos pos = new BlockPos(
                        centerX + dx * spacing * quadrant[0],
                        searchHeight.get(),
                        centerZ + dz * spacing * quadrant[1]
                    );
                    // Determine facing direction based on quadrant and position
                    Direction facing;
                    if (dx == halfX || dz == halfZ) {
                        // At edges, turn to next quadrant
                        if (dx == halfX) facing = quadrant[1] > 0 ? Direction.SOUTH : Direction.NORTH;
                        else facing = quadrant[0] > 0 ? Direction.EAST : Direction.WEST;
                    } else {
                        // Within quadrant, follow quadrant direction
                        facing = quadrant[0] > 0 ? Direction.EAST : Direction.WEST;
                    }

                    nodes.add(new PathNode(pos, facing, true, false, new ArrayList<>()));
                }
            }
        }
        return nodes;
    }

    private List<PathNode> generatePerimeterNodes(int nodesX, int nodesZ, int spacing) {
        List<PathNode> nodes = new ArrayList<>();
        int startX = minChunkX * 16;
        int startZ = minChunkZ * 16;

        for (int layer = 0; layer < Math.min(nodesX, nodesZ) / 2; layer++) {
            // Top edge
            for (int x = layer; x < nodesX - layer; x++) {
                nodes.add(new PathNode(new BlockPos(
                    startX + x * spacing,
                    searchHeight.get(),
                    startZ + layer * spacing
                ), x == nodesX - layer - 1 ? Direction.SOUTH : Direction.EAST, true, false, new ArrayList<>()));
            }
            // Right edge
            for (int z = layer; z < nodesZ - layer; z++) {
                nodes.add(new PathNode(new BlockPos(
                    startX + (nodesX - layer - 1) * spacing,
                    searchHeight.get(),
                    startZ + z * spacing
                ), z == nodesZ - layer - 1 ? Direction.WEST : Direction.SOUTH, true, false, new ArrayList<>()));
            }
            // Bottom edge
            for (int x = nodesX - layer - 1; x >= layer; x--) {
                nodes.add(new PathNode(new BlockPos(
                    startX + x * spacing,
                    searchHeight.get(),
                    startZ + (nodesZ - layer - 1) * spacing
                ), x == layer ? Direction.NORTH : Direction.WEST, true, false, new ArrayList<>()));
            }
            // Left edge
            for (int z = nodesZ - layer - 1; z > layer; z--) {
                nodes.add(new PathNode(new BlockPos(
                    startX + layer * spacing,
                    searchHeight.get(),
                    startZ + z * spacing
                ), z == layer + 1 ? Direction.EAST : Direction.NORTH, true, false, new ArrayList<>()));
            }
        }
        return nodes;
    }

    private List<PathNode> generateNearestNodes(int nodesX, int nodesZ, int spacing) {
        int startX = minChunkX * 16;
        int startZ = minChunkZ * 16;
        List<PathNode> tempNodes = new ArrayList<>();
        BlockPos center = new BlockPos(
            startX + (maxChunkX - minChunkX) * 8,
            searchHeight.get(),
            startZ + (maxChunkZ - minChunkZ) * 8
        );

        // Generate all nodes first
        for (int x = 0; x < nodesX; x++) {
            for (int z = 0; z < nodesZ; z++) {
                BlockPos pos = new BlockPos(
                    startX + x * spacing,
                    searchHeight.get(),
                    startZ + z * spacing
                );
                // Calculate facing direction based on position relative to center
                Vec3d directionVec = Vec3d.ofCenter(pos).subtract(Vec3d.ofCenter(center)).normalize();
                Direction facing = getClosestDirection(Rotations.getYaw(directionVec));

                tempNodes.add(new PathNode(pos, facing, true, false, new ArrayList<>()));
            }
        }

        // Sort by distance from center and add to grid nodes
        tempNodes.sort((a, b) -> Double.compare(
            center.getSquaredDistance(a.pos().getX(), a.pos().getY(), a.pos().getZ()),
            center.getSquaredDistance(b.pos().getX(), b.pos().getY(), b.pos().getZ())
        ));

        return tempNodes;
    }

    @EventHandler
    private void onRender(Render3DEvent event) {
        if (!initialized) return;

        // Update stats and render world overlays
        renderStats();
        renderPath(event);
        renderNodes(event);
        renderObstacles(event);

        // Always render line to next target node
        if (currentNode != null) {
            renderTargetLine(event);
        }

        // Render HUD with proper framebuffer state
        if (debugMessages.get()) {
            mc.getFramebuffer().beginWrite(true);
            renderHudText(event.matrices);

            // Render world overlays
            if (currentPath != null && showNodeConnections.get()) {
                renderPathLines(event);
            }
            renderGridOverlay(event);
            if (showPath.get() && currentNode != null) {
                renderCurrentNodePath(event);
            }
        }
    }

    private void renderStats() {
        updateStats();
        updatePathVisualization();
    }

    private void renderPath(Render3DEvent event) {
        if (!pathPoints.isEmpty()) {
            for (int i = 0; i < pathPoints.size() - 1; i++) {
                Vec3d current = pathPoints.get(i);
                Vec3d next = pathPoints.get(i + 1);
                event.renderer.line(
                    current.x, current.y, current.z,
                    next.x, next.y, next.z,
                    currentPathColor.get()
                );
            }
        }
    }

    private void renderNodes(Render3DEvent event) {
        int visitedCount = 0;
        int plannedCount = 0;

        for (PathNode node : gridNodes) {
            SettingColor color;
            double boxSize = 1.0;

            if (node == targetNode) {
                color = currentTargetColor.get();
                boxSize = 1.5;
                if (notifications.get() && showToasts.get()) {
                    String desc = String.format("Target Node: %s\nDirection: %s",
                        node.pos().toShortString(),
                        node.facing().toString());
                    showToast("Current Target", desc, Items.TARGET);
                }
            } else if (node.visited()) {
                color = visitedNodeColor.get();
                visitedCount++;
            } else if (currentPath != null && currentPath.nodes().contains(node)) {
                color = plannedNodeColor.get();
                plannedCount++;
            } else {
                color = nodeColor.get();
            }

            Vec3d center = Vec3d.ofCenter(node.pos());
            event.renderer.box(
                center.x - boxSize, center.y - boxSize, center.z - boxSize,
                center.x + boxSize, center.y + boxSize, center.z + boxSize,
                color,
                color,
                meteordevelopment.meteorclient.renderer.ShapeMode.Both,
                0
            );

            if (node == targetNode) {
                Vec3d direction = Vec3d.fromPolar(0, node.facing().yaw);
                Vec3d end = center.add(direction.multiply(2));
                event.renderer.line(
                    center.x, center.y, center.z,
                    end.x, end.y, end.z,
                    currentTargetColor.get()
                );
            }
        }
    }

    private void renderObstacles(Render3DEvent event) {
        int obstacleCount = 0;
        for (BlockPos obstacle : obstacles) {
            event.renderer.box(
                obstacle.getX(), obstacle.getY(), obstacle.getZ(),
                obstacle.getX() + 1, obstacle.getY() + 1, obstacle.getZ() + 1,
                obstacleColor.get(),
                obstacleColor.get(),
                meteordevelopment.meteorclient.renderer.ShapeMode.Both,
                0
            );
            obstacleCount++;
        }

        if (notifications.get() && showToasts.get()) {
            String desc = String.format("Active Obstacles: %d\nGrid Size: %dx%d",
                obstacleCount,
                (maxChunkX - minChunkX + 1) * 16,
                (maxChunkZ - minChunkZ + 1) * 16);
            showToast("Rendering Grid", desc, Items.BARRIER);
        }
    }


    @net.lenni0451.lambdaevents.EventHandler(priority = -1)
    public void onChunkData(ChunkDataEvent event) {
        try {
            WorldChunk chunk = event.chunk();
            if (chunk == null) return; // Prevent null chunk crashes

            long chunkLong = chunk.getPos().toLong();

            // Update cache
            if (seenChunksCache.getIfPresent(chunkLong) != null) return;
            seenChunksCache.put(chunkLong, Byte.MAX_VALUE);

            // Mark chunk as explored if within grid bounds
            ChunkPos chunkPos = chunk.getPos();
            if (isInGridBounds(chunkPos)) {
                if (!completedChunks.contains(chunkPos)) {
                    completedChunks.add(chunkPos);
                    exploredCount++;

                    // Mark nearby nodes as visited to improve blacklisting
                    markNearbyNodesVisited(chunkPos);

                    if (notifications.get() && showToasts.get()) {
                        String desc = String.format("Explored: %d/%d\nProgress: %.1f%%",
                            exploredCount, totalChunks,
                            (exploredCount * 100.0f) / totalChunks);
                        showToast("Exploration Progress", desc, Items.MAP);
                    }
                }
            }
        } catch (Exception e) {
            // Prevent chunk processing crashes
            showToast("Chunk Error", "Error processing chunk: " + e.getMessage(), Items.BARRIER);
        }
    }

    /**
     * Mark nodes near explored chunks as visited to improve blacklisting
     */
    private void markNearbyNodesVisited(ChunkPos chunkPos) {
        try {
            int chunkX = chunkPos.x * 16;
            int chunkZ = chunkPos.z * 16;
            int spacing = nodeSpacing.get();

            // Find nodes within this chunk and mark them as visited
            for (int i = 0; i < gridNodes.size(); i++) {
                PathNode node = gridNodes.get(i);
                BlockPos nodePos = node.pos();

                // Check if node is within the explored chunk
                if (nodePos.getX() >= chunkX && nodePos.getX() < chunkX + 16 &&
                    nodePos.getZ() >= chunkZ && nodePos.getZ() < chunkZ + 16) {

                    if (!node.visited()) {
                        gridNodes.set(i, node.withVisited(true));
                        if (notifications.get() && debugMessages.get()) {
                            showToast("Node Visited",
                                String.format("Auto-marked node at %s", nodePos.toShortString()),
                                Items.EMERALD);
                        }
                    }
                }
            }

            // Save state after marking nodes
            updateExplorationState();
        } catch (Exception e) {
            showToast("Node Marking Error", e.getMessage(), Items.BARRIER);
        }
    }

    private void handleChunkCompletion() {
        completedChunks.add(currentTarget);
        unexploredChunks.remove(currentTarget);
        exploredCount++;

        if (notifications.get() && showToasts.get()) {
            String desc = String.format("Chunks: %d/%d\nProgress: %.1f%%",
                exploredCount, totalChunks,
                (exploredCount * 100.0f) / totalChunks);
            showToast("Chunk Complete", desc, Items.MAP);
        }

        updateExplorationPhase();
        selectNextTarget();
    }

    private void updateExplorationPhase() {
        if (currentPhase == ExplorationPhase.PERIMETER && !hasPerimeterChunks()) {
            currentPhase = ExplorationPhase.SNAKE_PATTERN;
            generateSnakePatternChunks();
        } else if (currentPhase == ExplorationPhase.SNAKE_PATTERN && unexploredChunks.isEmpty()) {
            if (notifications.get()) {
                showToast("Phase Complete",
                    String.format("Explored all %d chunks\nMoving to next phase", totalChunks),
                    Items.EMERALD);
            }
            currentPhase = ExplorationPhase.COMPLETED;
        }
    }

    private boolean hasPerimeterChunks() {
        return unexploredChunks.stream().anyMatch(this::isPerimeterChunk);
    }

    private boolean isPerimeterChunk(ChunkPos pos) {
        return pos.x == minChunkX || pos.x == maxChunkX ||
               pos.z == minChunkZ || pos.z == maxChunkZ;
    }

    private void generateSnakePatternChunks() {
        if (notifications.get()) {
            showToast("Pattern Generation", "Starting snake pattern", Items.MAP);
        }
        boolean goingEast = true;
        for (int z = minChunkZ + 1; z < maxChunkZ; z++) {
            if (goingEast) {
                for (int x = minChunkX + 1; x < maxChunkX; x++) {
                    ChunkPos chunk = new ChunkPos(x, z);
                    if (!completedChunks.contains(chunk)) {
                        unexploredChunks.add(chunk);
                    }
                }
            } else {
                for (int x = maxChunkX - 1; x > minChunkX; x--) {
                    ChunkPos chunk = new ChunkPos(x, z);
                    if (!completedChunks.contains(chunk)) {
                        unexploredChunks.add(chunk);
                    }
                }
            }
            goingEast = !goingEast;
        }
        if (notifications.get()) {
            showToast("Pattern Complete", unexploredChunks.size() + " chunks to explore", Items.MAP);
        }
    }

    private void selectNextTarget() {
        if (unexploredChunks.isEmpty()) {
            currentPhase = ExplorationPhase.COMPLETED;
            return;
        }

        ChunkPos playerChunk = mc.player.getChunkPos();

        if (currentPhase == ExplorationPhase.PERIMETER) {
            currentTarget = unexploredChunks.stream()
                .filter(this::isPerimeterChunk)
                .min((a, b) -> compareChunkDistance(playerChunk, a, b))
                .orElse(null);
        } else {
            currentTarget = unexploredChunks.stream()
                .min((a, b) -> compareChunkDistance(playerChunk, a, b))
                .orElse(null);
        }

        if (currentTarget != null) {
            Direction newDirection = calculateDirection(playerChunk, currentTarget);
            if (newDirection != currentDirection) {
                queueCommand(".rotation set " + newDirection.command);
                currentDirection = newDirection;
            }
        }
    }

    private int compareChunkDistance(ChunkPos from, ChunkPos a, ChunkPos b) {
        return Double.compare(
            getChunkDistance(from, a),
            getChunkDistance(from, b)
        );
    }

    private double getChunkDistance(ChunkPos a, ChunkPos b) {
        return Math.sqrt(Math.pow(a.x - b.x, 2) + Math.pow(a.z - b.z, 2));
    }

    private Direction getClosestDirection(double yaw) {
        yaw = (yaw % 360 + 360) % 360; // Normalize to 0-360

        Direction closest = Direction.NORTH;
        double minDiff = Double.MAX_VALUE;

        for (Direction dir : Direction.values()) {
            double diff = Math.abs(yaw - dir.yaw);
            if (diff > 180) diff = 360 - diff;
            if (diff < minDiff) {
                minDiff = diff;
                closest = dir;
            }
        }
        return closest;
    }

    private Direction calculateDirection(ChunkPos from, ChunkPos to) {
        int dx = to.x - from.x;
        int dz = to.z - from.z;

        // Choose direction based on larger distance component
        if (Math.abs(dx) > Math.abs(dz)) {
            return dx > 0 ? Direction.EAST : Direction.WEST;
        } else {
            return dz > 0 ? Direction.SOUTH : Direction.NORTH;
        }
    }

    private Vec3d calculateAveragePosition(ArrayDeque<Vec3d> positions) {
        double sumX = 0, sumZ = 0;
        for (Vec3d pos : positions) {
            sumX += pos.x;
            sumZ += pos.z;
        }
        return new Vec3d(sumX / positions.size(), searchHeight.get(), sumZ / positions.size());
    }



    private void updateNavigation() {
        // Skip if commands pending
        if (commandState != CommandState.IDLE || !commandQueue.isEmpty()) {
            return;
        }

        // Check and update current path/node state
        if (currentPath == null || currentPath.isComplete()) {
            generateNextPath();
            if (currentPath == null || currentPath.isComplete()) {
                showToast("Navigation", "Path not found - Replanning route", Items.COMPASS);
                return;
            }
        }

        // Update current target node from path
        currentNode = currentPath.current();
        if (currentNode == null) {
            generateNextPath();
            return;
        }

        Vec3d playerVec = mc.player.getPos();
        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos targetPos = currentNode.pos();
        Vec3d targetVec = Vec3d.ofCenter(targetPos);

        // Check if we've reached current node
        double distanceToTarget = playerVec.distanceTo(targetVec);
        if (distanceToTarget <= nodeReachDistance.get()) {
            if (isRotationAligned(currentDirection)) {
                handleNodeReached();
                return;
            } else {
                queueCommand(".rotation set " + currentDirection.command);
                return;
            }
        } else if (distanceToTarget > nodeSpacing.get() * 2) {
            generateNextPath();
            return;
        }

        // Handle grid alignment and movement
        BlockPos alignedPos = getClosestGridPoint(playerPos);
        if (!playerPos.equals(alignedPos)) {
            if (autoWalkEnabled) {
                toggleAutoWalk(false);
            }
            alignWithGrid();
            return;
        }

        // Get next grid-aligned position towards target
        BlockPos nextPos = getNextGridPosition(playerPos, targetPos);
        Direction optimalDir = calculateGridDirection(playerPos, nextPos);

        // Check if we need to change direction
        if (optimalDir != currentDirection) {
            toggleAutoWalk(false);
            queueCommand(".rotation set " + optimalDir.command);
            currentDirection = optimalDir;
            waitTicks = 5;
            return;
        }

        // Ensure rotation is precise
        if (!isRotationAligned(currentDirection)) {
            queueCommand(".rotation set " + currentDirection.command);
            waitTicks = 5;
            return;
        }

        // Start/continue movement once aligned
        if (!autoWalkEnabled) {
            toggleAutoWalk(true);
        }

        // Safety check - detect if stuck or off course
        if (stuckTicks > STUCK_THRESHOLD / 2) {
            resetState();
            generateNextPath();
            return;
        }
    }

    private boolean shouldFindNewNode() {
        if (currentNode == null) return true;
        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = Vec3d.ofCenter(currentNode.pos());

        // Check if we're stuck or too far from target
        return stuckTicks > STUCK_THRESHOLD / 2 ||
               playerPos.distanceTo(targetPos) > nodeSpacing.get() * 2;
    }

    private void handleNodeReached() {
        try {
            // Mark current node as visited with improved tracking
            if (currentNode != null && currentNodeIndex >= 0 && currentNodeIndex < gridNodes.size()) {
                PathNode visitedNode = currentNode.withVisited(true);
                gridNodes.set(currentNodeIndex, visitedNode);

                // Also mark in completed chunks for redundancy
                ChunkPos nodeChunk = new ChunkPos(currentNode.pos());
                if (!completedChunks.contains(nodeChunk)) {
                    completedChunks.add(nodeChunk);
                }

                if (notifications.get()) {
                    int visitedCount = (int) gridNodes.stream().filter(PathNode::visited).count();
                    showToast("Node Complete",
                        String.format("Reached %s (%d/%d visited)",
                            currentNode.pos().toShortString(), visitedCount, gridNodes.size()),
                        Items.COMPASS);
                }
            }

            // Update persistent state immediately
            updateExplorationState();
            saveCurrentState();

            // Stop movement and update path
            toggleAutoWalk(false);

            if (currentPath != null && !currentPath.isComplete()) {
                currentPath = currentPath.advance();
                if (!currentPath.isComplete()) {
                    targetNode = currentPath.current();
                    return;
                }
            }

            // Generate new path if current one is complete
            generateNextPath();
        } catch (Exception e) {
            showToast("Node Completion Error", e.getMessage(), Items.BARRIER);
            resetState();
        }
    }

    /**
     * Enhanced state management methods
     */
    private void updateExplorationState() {
        synchronized (stateLock) {
            List<PathNode> visited = gridNodes.stream()
                .filter(PathNode::visited)
                .collect(Collectors.toList());

            List<PathNode> remaining = gridNodes.stream()
                .filter(n -> !n.visited())
                .collect(Collectors.toList());

            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("algorithm", algorithm.get().name());
            additionalData.put("nodeSpacing", nodeSpacing.get());
            additionalData.put("visitOrder", visitOrder.get().name());
            additionalData.put("gridRadius", gridChunkRadius.get());

            savedState = new ExplorationState(
                visited,
                remaining,
                mc.player != null ? mc.player.getBlockPos() : BlockPos.ORIGIN,
                currentDirection,
                currentPhase,
                startTime,
                totalDistance,
                exploredCount,
                additionalData
            );
        }
    }

    /**
     * Attempts to restore exploration state from previous session
     */
    private boolean tryRestoreState() {
        synchronized (stateLock) {
            if (savedState == null) return false;

            try {
                // Restore basic state
                currentPhase = savedState.phase();
                currentDirection = savedState.currentDirection();
                startTime = savedState.startTime();
                totalDistance = savedState.totalDistance();
                exploredCount = savedState.exploredChunks();

                // Restore node states
                Map<BlockPos, Boolean> visitedMap = savedState.visitedNodes().stream()
                    .collect(Collectors.toMap(PathNode::pos, n -> true));

                for (int i = 0; i < gridNodes.size(); i++) {
                    PathNode node = gridNodes.get(i);
                    boolean wasVisited = visitedMap.getOrDefault(node.pos(), false);
                    if (wasVisited != node.visited()) {
                        gridNodes.set(i, node.withVisited(wasVisited));
                    }
                }

                if (notifications.get()) {
                    try {
                        float percentage = (float)savedState.getCompletionPercentage();
                        showToast("State Restored", String.format("%.1f", percentage) + "%% complete", Items.EMERALD);
                    } catch (Exception e) {
                        showToast("State Restored", "Previous session restored", Items.EMERALD);
                    }
                }

                return true;
            } catch (Exception e) {
                showToast("Error", "Failed to restore state: " + e.getMessage(), Items.BARRIER);
                savedState = null;
                return false;
            }
        }
    }

    /**
     * Saves current state for persistence across sessions
     */
    private void saveCurrentState() {
        updateExplorationState();
        if (notifications.get() && savedState != null) {
            try {
                float percentage = (float)savedState.getCompletionPercentage();
                showToast("State Saved", String.format("Progress: %.1f%%", percentage), Items.EMERALD);
            } catch (Exception e) {
                showToast("State Saved", "Current progress saved", Items.EMERALD);
            }
        }
    }

    /**
     * Provides detailed progress reporting
     */
    private void reportProgress() {
        if (stats == null) return;

        synchronized (stateLock) {
            double completionPercent = savedState != null ?
                savedState.getCompletionPercentage() :
                (stats.nodesVisited() * 100.0) / (stats.nodesVisited() + stats.nodesRemaining());

            long elapsedTime = System.currentTimeMillis() - startTime;
            double avgSpeed = totalDistance / Math.max(1, elapsedTime / 1000.0);

            // Estimate time remaining based on current progress
            double remainingPercent = 100.0 - completionPercent;
            long estimatedTimeRemaining = remainingPercent > 0 ?
                (long)(elapsedTime * remainingPercent / completionPercent) : 0;

            try {
                if (notifications.get() && showToasts.get()) {
                    String timeRemainingStr = estimatedTimeRemaining > 0 ? formatDuration(estimatedTimeRemaining) : "Unknown";
                    String toastDesc = String.format("%.1f%% Complete\n%s remaining", completionPercent, timeRemainingStr);
                    showToast("Progress Report", toastDesc, Items.CLOCK);
                }
            } catch (Exception e) {
                // Silent error handling for progress reports
            }
        }
    }

    private BlockPos getNextGridPosition(BlockPos current, BlockPos target) {
        int spacing = nodeSpacing.get();
        int dx = target.getX() - current.getX();
        int dz = target.getZ() - current.getZ();

        // Handle exact alignment
        int xMod = Math.abs(current.getX() % spacing);
        int zMod = Math.abs(current.getZ() % spacing);
        if (xMod > 0 || zMod > 0) {
            // Move to nearest grid point first
            return getClosestGridPoint(current);
        }

        // Move along longer distance first for efficiency
        if (Math.abs(dx) > Math.abs(dz)) {
            int nextX = current.getX() + spacing * Integer.signum(dx);
            return new BlockPos(nextX, current.getY(), current.getZ());
        } else {
            int nextZ = current.getZ() + spacing * Integer.signum(dz);
            return new BlockPos(current.getX(), current.getY(), nextZ);
        }
    }

    private boolean isAlignedWithGrid(BlockPos pos) {
        int spacing = nodeSpacing.get();
        double alignmentThreshold = Math.min(1.5, spacing * 0.1); // Scale with node spacing

        // Get exact distances to nearest grid lines
        double xOffset = Math.abs(pos.getX() % spacing);
        double zOffset = Math.abs(pos.getZ() % spacing);
        xOffset = Math.min(xOffset, spacing - xOffset);
        zOffset = Math.min(zOffset, spacing - zOffset);

        if (xOffset > alignmentThreshold || zOffset > alignmentThreshold) {
            return false;
        }
        return true;
    }

    private BlockPos getClosestGridPoint(BlockPos pos) {
        int spacing = nodeSpacing.get();
        double x = pos.getX();
        double z = pos.getZ();

        // Calculate closest grid intersections
        int gridX = (int)(Math.round(x / (double)spacing) * spacing);
        int gridZ = (int)(Math.round(z / (double)spacing) * spacing);

        return new BlockPos(gridX, pos.getY(), gridZ);
    }

    private void alignWithGrid() {
        BlockPos currentPos = mc.player.getBlockPos();
        BlockPos targetPos = getClosestGridPoint(currentPos);
        Vec3d playerVec = mc.player.getPos();
        Vec3d targetVec = Vec3d.ofCenter(targetPos);
        Vec3d moveVec = targetVec.subtract(playerVec);
        double dist = moveVec.horizontalLength();

        // Grid alignment debug removed

        // Ensure we're fully stopped first
        if (autoWalkEnabled) {
            toggleAutoWalk(false);
            waitTicks = 10;
            return;
        }

        // Check if we need to rotate
        Direction alignDir = getClosestDirection(Rotations.getYaw(moveVec));
        if (currentDirection != alignDir) {
            queueCommand(".rotation set " + alignDir.command);
            currentDirection = alignDir;
            waitTicks = 5;
            return;
        }

        // Verify rotation is precise before moving
        if (!isRotationAligned(currentDirection)) {
            queueCommand(".rotation set " + currentDirection.command);
            waitTicks = 5;
            return;
        }

        // Apply careful movement if needed
        if (dist > 0.1) {
            // Calculate speed based on distance
            double speed = Math.min(0.15, dist * 0.3); // Gradual slowdown
            Vec3d normalized = moveVec.normalize();

            // Verify movement aligns with grid
            double gridAlignmentError = Math.abs(normalized.x) + Math.abs(normalized.z);
            if (gridAlignmentError > 1.1) { // Allow slight deviation
                // Snap to cardinal direction
                normalized = switch(currentDirection) {
                    case NORTH -> new Vec3d(0, 0, -1);
                    case SOUTH -> new Vec3d(0, 0, 1);
                    case EAST -> new Vec3d(1, 0, 0);
                    case WEST -> new Vec3d(-1, 0, 0);
                };
            }

            // Apply controlled movement
            mc.player.setVelocity(
                normalized.x * speed,
                mc.player.getVelocity().y,
                normalized.z * speed
            );

            // Add momentum damping when very close
            if (dist < 0.5) {
                mc.player.setVelocity(
                    mc.player.getVelocity().multiply(0.7, 1, 0.7)
                );
            }
        }
    }

    private double calculateNodeScore(BlockPos from, PathNode node) {
        double distance = from.getSquaredDistance(node.pos());
        int turns = countDirectionChanges(from, node.pos());
        return distance * (1 + turns * 0.3); // Penalize paths with more turns
    }

    private List<BlockPos> getGridPointsBetween(BlockPos start, BlockPos end) {
        List<BlockPos> points = new ArrayList<>();
        int spacing = nodeSpacing.get();

        // Move in grid increments, preferring longer axis first
        BlockPos current = start;
        while (!current.equals(end)) {
            int dx = end.getX() - current.getX();
            int dz = end.getZ() - current.getZ();

            if (Math.abs(dx) > Math.abs(dz)) {
                int nextX = current.getX() + spacing * Integer.signum(dx);
                current = new BlockPos(nextX, current.getY(), current.getZ());
            } else {
                int nextZ = current.getZ() + spacing * Integer.signum(dz);
                current = new BlockPos(current.getX(), current.getY(), nextZ);
            }

            if (!current.equals(end)) {
                points.add(current);
            }
        }

        return points;
    }

    private Direction calculateGridDirection(BlockPos from, BlockPos to) {
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        // Use exact cardinal directions for grid movement
        if (Math.abs(dx) > Math.abs(dz)) {
            return dx > 0 ? Direction.EAST : Direction.WEST;
        } else {
            return dz > 0 ? Direction.SOUTH : Direction.NORTH;
        }
    }

    private boolean isValidNodeTarget(BlockPos pos) {
        if (!isInGridBounds(new ChunkPos(pos))) return false;

        // Check if we can reach this node along grid lines
        BlockPos playerPos = mc.player.getBlockPos();
        int spacing = nodeSpacing.get();
        int dx = Math.abs(pos.getX() - playerPos.getX());
        int dz = Math.abs(pos.getZ() - playerPos.getZ());

        // Must be reachable via grid movements
        return dx % spacing == 0 && dz % spacing == 0;
    }

    private int compareNodeTargets(BlockPos current, PathNode a, PathNode b) {
        // Prefer nodes that don't require direction changes
        double directionPenalty = 0.3; // Lower = more emphasis on direction

        int aChanges = countDirectionChanges(current, a.pos());
        int bChanges = countDirectionChanges(current, b.pos());

        double aScore = current.getSquaredDistance(a.pos()) * (1 + aChanges * directionPenalty);
        double bScore = current.getSquaredDistance(b.pos()) * (1 + bChanges * directionPenalty);

        return Double.compare(aScore, bScore);
    }

    private int countDirectionChanges(BlockPos from, BlockPos to) {
        int changes = 0;
        BlockPos current = from;
        Direction lastDir = null;

        while (!current.equals(to)) {
            Direction nextDir = calculateGridMovement(current, to);
            if (lastDir != null && nextDir != lastDir) changes++;
            lastDir = nextDir;

            // Move one grid step
            int spacing = nodeSpacing.get();
            current = switch (nextDir) {
                case NORTH -> current.add(0, 0, -spacing);
                case SOUTH -> current.add(0, 0, spacing);
                case EAST -> current.add(spacing, 0, 0);
                case WEST -> current.add(-spacing, 0, 0);
            };
        }

        return changes;
    }

    private Direction calculateOptimalApproach(BlockPos from, BlockPos to) {
        // Always prefer movement along longer axis first
        int dx = to.getX() - from.getX();
        int dz = to.getZ() - from.getZ();

        if (Math.abs(dx) > Math.abs(dz)) {
            return dx > 0 ? Direction.EAST : Direction.WEST;
        } else {
            return dz > 0 ? Direction.SOUTH : Direction.NORTH;
        }
    }

    private List<PathNode> optimizeNodePath(List<PathNode> candidates) {
        if (candidates.isEmpty()) return new ArrayList<>();

        // Start with nearest node
        List<PathNode> optimized = new ArrayList<>();
        optimized.add(candidates.get(0));
        List<PathNode> remaining = new ArrayList<>(candidates);
        remaining.remove(0);

        // Build path by finding best next node
        while (!remaining.isEmpty()) {
            BlockPos lastPos = optimized.get(optimized.size() - 1).pos();
            PathNode best = null;
            double bestScore = Double.MAX_VALUE;

            for (PathNode candidate : remaining) {
                double distanceScore = lastPos.getSquaredDistance(candidate.pos());
                int directionChanges = countDirectionChanges(lastPos, candidate.pos());
                double score = distanceScore * (1 + directionChanges * 0.3);

                // Consider grid alignment in scoring
                if (!isValidNodeTarget(candidate.pos())) {
                    score *= 2; // Penalize nodes that aren't grid-aligned
                }

                if (score < bestScore) {
                    bestScore = score;
                    best = candidate;
                }
            }

            if (best != null) {
                optimized.add(best);
                remaining.remove(best);
            } else {
                break; // No valid candidates found
            }
        }

        // Path optimization debug removed

        return optimized;
    }

    private boolean isRotationAligned(Direction dir) {
        float currentYaw = (mc.player.getYaw() % 360 + 360) % 360;
        return Math.abs(currentYaw - dir.yaw) <= 2;
    }

    private Direction calculateGridMovement(BlockPos current, BlockPos target) {
        int dx = target.getX() - current.getX();
        int dz = target.getZ() - current.getZ();
        int spacing = nodeSpacing.get();

        // Only move in cardinal directions along grid lines
        if (Math.abs(dx) >= spacing) {
            return dx > 0 ? Direction.EAST : Direction.WEST;
        } else if (Math.abs(dz) >= spacing) {
            return dz > 0 ? Direction.SOUTH : Direction.NORTH;
        } else {
            // If very close to target, face it directly
            return getClosestDirection(Rotations.getYaw(
                Vec3d.ofCenter(target).subtract(Vec3d.ofCenter(current))
            ));
        }
    }

    /**
     * Optimized pathfinding with caching and performance monitoring
     */
    private List<PathNode> findPath(BlockPos start, BlockPos target) {
        pathfindingCalls++;

        // Check cache first
        PathCacheKey cacheKey = new PathCacheKey(
            start, target, algorithm.get(), nodeSpacing.get(), System.currentTimeMillis()
        );

        List<PathNode> cachedPath = getCachedPath(cacheKey);
        if (cachedPath != null) {
            cacheHits++;
            return new ArrayList<>(cachedPath); // Return copy to avoid modification
        }

        cacheMisses++;
        long startTime = System.nanoTime();
        List<PathNode> path = null;

        try {
            // Use simple direct path for grid-based movement
            // No need for complex pathfinding since we're using a structured grid
            path = findDirectPath(start, target);

            // If direct path fails, create a simple grid-aligned path
            if (path == null || path.isEmpty()) {
                path = createGridAlignedPath(start, target);
            }
        } catch (Exception e) {
            showToast("Pathfinding Error", e.getMessage(), Items.BARRIER);
            return null;
        }

        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        totalPathfindingTime += duration;

        // Track performance
        pathfindTimes.offer(duration);
        if (pathfindTimes.size() > MAX_PATHFIND_SAMPLES) {
            pathfindTimes.poll();
        }

        // Cache successful paths
        if (path != null && path.size() > 1) {
            cachePathResult(cacheKey, path);

            // Path found - debug messages removed
        }

        return path;
    }

    /**
     * Retrieves cached path if available and not expired
     */
    private List<PathNode> getCachedPath(PathCacheKey key) {
        // Clean expired entries periodically
        if (pathfindingCalls % 100 == 0) {
            cleanExpiredCache();
        }

        for (Map.Entry<PathCacheKey, List<PathNode>> entry : pathCache.entrySet()) {
            PathCacheKey cachedKey = entry.getKey();

            // Check if this is a suitable cached path
            if (cachedKey.start().equals(key.start()) &&
                cachedKey.target().equals(key.target()) &&
                cachedKey.algorithm() == key.algorithm() &&
                cachedKey.nodeSpacing() == key.nodeSpacing() &&
                !cachedKey.isExpired()) {

                return entry.getValue();
            }
        }

        return null;
    }

    /**
     * Caches a pathfinding result
     */
    private void cachePathResult(PathCacheKey key, List<PathNode> path) {
        if (pathCache.size() >= MAX_CACHE_SIZE) {
            // Remove oldest entries
            pathCache.entrySet().removeIf(entry -> entry.getKey().isExpired());

            // If still too large, remove some random entries
            if (pathCache.size() >= MAX_CACHE_SIZE) {
                Iterator<Map.Entry<PathCacheKey, List<PathNode>>> iterator = pathCache.entrySet().iterator();
                for (int i = 0; i < MAX_CACHE_SIZE / 4 && iterator.hasNext(); i++) {
                    iterator.next();
                    iterator.remove();
                }
            }
        }

        pathCache.put(key, new ArrayList<>(path)); // Store copy
    }

    /**
     * Removes expired cache entries
     */
    private void cleanExpiredCache() {
        pathCache.entrySet().removeIf(entry -> entry.getKey().isExpired());
    }

    /**
     * Simple direct path for short distances
     */
    private List<PathNode> findDirectPath(BlockPos start, BlockPos target) {
        List<PathNode> path = new ArrayList<>();

        // Check if direct path is clear
        List<BlockPos> directPoints = getGridPointsBetween(start, target);
        boolean pathClear = directPoints.stream().allMatch(this::isValidPosition);

        if (pathClear) {
            for (BlockPos point : directPoints) {
                Direction facing = calculateGridDirection(start, point);
                path.add(new PathNode(point, facing, true, false, new ArrayList<>()));
            }
        }

        return path.isEmpty() ? null : path;
    }

    /**
     * Creates a simple grid-aligned path between two points
     * This replaces complex pathfinding algorithms for our grid system
     */
    private List<PathNode> createGridAlignedPath(BlockPos start, BlockPos target) {
        List<PathNode> path = new ArrayList<>();

        try {
            BlockPos current = getClosestGridPoint(start);
            BlockPos end = getClosestGridPoint(target);
            int spacing = nodeSpacing.get();

            // Create path by moving along grid lines
            while (!current.equals(end)) {
                // Add current position to path
                Direction facing = calculateGridDirection(current, end);
                path.add(new PathNode(current, facing, true, false, new ArrayList<>()));

                // Move towards target along grid
                int dx = end.getX() - current.getX();
                int dz = end.getZ() - current.getZ();

                if (Math.abs(dx) > Math.abs(dz)) {
                    // Move horizontally
                    current = current.add(dx > 0 ? spacing : -spacing, 0, 0);
                } else {
                    // Move vertically
                    current = current.add(0, 0, dz > 0 ? spacing : -spacing);
                }

                // Safety check to prevent infinite loops
                if (path.size() > 1000) {
                    showToast("Path Error", "Path too long - stopping", Items.BARRIER);
                    break;
                }
            }

            // Add final target
            if (!path.isEmpty()) {
                Direction finalFacing = path.get(path.size() - 1).facing();
                path.add(new PathNode(end, finalFacing, true, false, new ArrayList<>()));
            }

        } catch (Exception e) {
            showToast("Grid Path Error", e.getMessage(), Items.BARRIER);
        }

        return path;
    }

    /**
     * Performance monitoring methods
     */
    private void logPerformanceStats() {
        if (pathfindingCalls > 0 && showToasts.get()) {
            try {
                double avgTime = totalPathfindingTime / (double) pathfindingCalls / 1_000_000.0;
                double cacheHitRate = (cacheHits * 100.0) / (cacheHits + cacheMisses);

                StringBuilder stats = new StringBuilder();
                String performance = String.format("Calls: %d\nAvg Time: %.1f ms\nCache Hit Rate: %.1f%%",
                    pathfindingCalls, avgTime, cacheHitRate);
                stats.append(performance);

                if (debugMessages.get()) {
                    stats.append("\nCache Size: ").append(pathCache.size());
                }

                showToast("Performance Stats", stats.toString(), Items.CLOCK);
            } catch (Exception e) {
                showToast("Performance Stats", "Error calculating stats", Items.BARRIER);
            }
        }
    }

    private List<PathNode> findPathAStar(BlockPos start, BlockPos target) {
        PriorityQueue<AStarNode> openSet = new PriorityQueue<>(Comparator.comparingDouble(n -> n.fScore));
        Map<BlockPos, AStarNode> nodeMap = new HashMap<>();
        Set<BlockPos> closedSet = new HashSet<>();

        // Initialize start node
        AStarNode startNode = new AStarNode(start, null);
        startNode.gScore = 0;
        startNode.fScore = heuristic(start, target);
        openSet.add(startNode);
        nodeMap.put(start, startNode);

        while (!openSet.isEmpty()) {
            AStarNode current = openSet.poll();

            if (current.pos.equals(target)) {
                return reconstructPath(current);
            }

            closedSet.add(current.pos);

            // Generate neighbors based on movement rules
            for (BlockPos neighbor : getValidNeighbors(current.pos)) {
                if (closedSet.contains(neighbor)) continue;

                double tentativeG = current.gScore + getMovementCost(current.pos, neighbor);
                AStarNode neighborNode = nodeMap.getOrDefault(neighbor, new AStarNode(neighbor, current));

                if (!nodeMap.containsKey(neighbor) || tentativeG < neighborNode.gScore) {
                    neighborNode.parent = current;
                    neighborNode.gScore = tentativeG;
                    neighborNode.fScore = tentativeG + heuristic(neighbor, target);

                    nodeMap.put(neighbor, neighborNode);
                    openSet.add(neighborNode);
                }
            }
        }

        return new ArrayList<>(); // No path found
    }

    private class AStarNode {
        BlockPos pos;
        AStarNode parent;
        double gScore = Double.POSITIVE_INFINITY;
        double fScore = Double.POSITIVE_INFINITY;

        AStarNode(BlockPos pos, AStarNode parent) {
            this.pos = pos;
            this.parent = parent;
        }
    }

    private double heuristic(BlockPos a, BlockPos b) {
        if (allowDiagonal.get()) {
            // Octile distance for diagonal movement
            double dx = Math.abs(a.getX() - b.getX());
            double dz = Math.abs(a.getZ() - b.getZ());
            return Math.max(dx, dz) + (Math.sqrt(2) - 1) * Math.min(dx, dz);
        } else {
            // Manhattan distance for cardinal movement
            return Math.abs(a.getX() - b.getX()) + Math.abs(a.getZ() - b.getZ());
        }
    }

    private List<BlockPos> getValidNeighbors(BlockPos pos) {
        List<BlockPos> neighbors = new ArrayList<>();
        int spacing = nodeSpacing.get();

        // Cardinal directions
        BlockPos[] cardinals = {
            pos.add(spacing, 0, 0),  // East
            pos.add(-spacing, 0, 0), // West
            pos.add(0, 0, spacing),  // South
            pos.add(0, 0, -spacing)  // North
        };

        for (BlockPos n : cardinals) {
            if (isValidPosition(n)) {
                neighbors.add(n);
            }
        }

        // Diagonal neighbors if enabled
        if (allowDiagonal.get()) {
            BlockPos[] diagonals = {
                pos.add(spacing, 0, spacing),   // SE
                pos.add(spacing, 0, -spacing),  // NE
                pos.add(-spacing, 0, spacing),  // SW
                pos.add(-spacing, 0, -spacing)  // NW
            };

            for (BlockPos n : diagonals) {
                if (isValidPosition(n)) {
                    neighbors.add(n);
                }
            }
        }

        return neighbors;
    }

    private boolean isValidPosition(BlockPos pos) {
        // Check grid bounds
        if (!isInGridBounds(new ChunkPos(pos))) return false;

        // Check wall distance
        int minDist = wallDistance.get();
        if (pos.getX() <= minChunkX * 16 + minDist || pos.getX() >= maxChunkX * 16 - minDist ||
            pos.getZ() <= minChunkZ * 16 + minDist || pos.getZ() >= maxChunkZ * 16 - minDist) {
            return false;
        }

        // Check obstacles
        return !obstacles.contains(pos);
    }

    private double getMovementCost(BlockPos from, BlockPos to) {
        if (from.getX() != to.getX() && from.getZ() != to.getZ()) {
            return Math.sqrt(2); // Diagonal movement
        }
        return 1.0; // Cardinal movement
    }

    private List<PathNode> reconstructPath(AStarNode endNode) {
        List<PathNode> path = new ArrayList<>();
        AStarNode current = endNode;

        while (current != null) {
            Direction facing = current.parent != null ?
                calculateGridDirection(current.parent.pos, current.pos) :
                currentDirection;

            path.add(0, new PathNode(
                current.pos,
                facing,
                true,
                false,
                new ArrayList<>()
            ));
            current = current.parent;
        }

        return path;
    }

    private void generateNextPath() {
        if (gridNodes.isEmpty()) return;

        BlockPos playerPos = mc.player.getBlockPos();
        BlockPos alignedPlayerPos = getClosestGridPoint(playerPos);

        if (notifications.get()) {
            showToast("Path Planning", "Calculating next path", Items.COMPASS);
        }

        // Find candidate nodes based on selection mode
        List<PathNode> candidates = switch(nodeSelection.get()) {
            case ALL -> gridNodes.stream()
                .filter(n -> !n.visited())
                .filter(n -> isValidNodeTarget(n.pos()))
                .collect(Collectors.toList());

            case SPECIFIED -> gridNodes.stream()
                .filter(n -> !n.visited() && isSpecifiedTarget(n.pos()))
                .collect(Collectors.toList());

            case DYNAMIC -> selectDynamicTargets(alignedPlayerPos);
        };

        if (candidates.isEmpty()) {
            showToast("SearchBot", "All accessible nodes explored!", Items.EMERALD);
            currentPhase = ExplorationPhase.COMPLETED;
            return;
        }

        if (notifications.get() && debugMessages.get()) {
            showToast("Path Options", String.format("Found %d candidate nodes", candidates.size()), Items.MAP);
        }

        // Create optimized path through nodes
        List<PathNode> optimizedPath = optimizeNodePath(candidates);
        if (optimizedPath.isEmpty()) {
            showToast("Path Error", "Failed to optimize path - using fallback", Items.BARRIER);
            optimizedPath.add(candidates.get(0));
        }

        // Build path with intermediate grid points
        List<PathNode> fullPath = new ArrayList<>();
        BlockPos currentPos = alignedPlayerPos;

        for (PathNode targetNode : optimizedPath) {
            // Add intermediate nodes along grid lines
            List<BlockPos> gridPoints = getGridPointsBetween(currentPos, targetNode.pos());
            for (BlockPos gridPoint : gridPoints) {
                Direction moveDir = calculateGridDirection(currentPos, gridPoint);
                fullPath.add(new PathNode(
                    gridPoint,
                    moveDir,
                    true,
                    false,
                    new ArrayList<>()
                ));
                currentPos = gridPoint;
            }

            // Add target node
            Direction finalDir = calculateGridDirection(currentPos, targetNode.pos());
            fullPath.add(new PathNode(
                targetNode.pos(),
                finalDir,
                true,
                false,
                new ArrayList<>()
            ));
            currentPos = targetNode.pos();
        }

        currentPath = new NodePath(fullPath, 0);
        targetNode = fullPath.get(0);

        if (notifications.get() && debugMessages.get()) {
            String pathInfo = "Length: " + fullPath.size() + " nodes\n" +
                            "First target: " + targetNode.pos().toShortString();
            showToast("Path Generated", pathInfo, Items.FILLED_MAP);
        }
    }

    private void queueCommand(String cmd) {
        // Prevent duplicate commands in queue
        if (!commandQueue.contains(cmd)) {
            commandQueue.add(cmd);
            if (notifications.get() && debugMessages.get()) {
                showToast("Command Queued", cmd, Items.COMMAND_BLOCK);
            }
        }
    }

    private enum CommandExecutionState {
        WAITING,
        OPEN_CHAT,
        TYPING,
        ENTER_DOWN,
        ENTER_UP,
        DONE
    }

    private CommandExecutionState execState = CommandExecutionState.DONE;
    private int execTimer = 0;
    private String pendingCommand = "";
    private String currentTypedText = "";

    private void sendCommand(String cmd) {
        if (mc.player == null) return;

        // Don't queue new commands if still executing
        if (execState != CommandExecutionState.DONE) {
            queueCommand(cmd);
            return;
        }

        if (notifications.get() && debugMessages.get()) {
            showToast("Executing Command", cmd, Items.COMMAND_BLOCK);
        }

        // Update tracking states first
        if (cmd.startsWith(".toggle auto-walk")) {
            boolean enabling = cmd.endsWith("on");
            if (enabling == autoWalkEnabled) {
                if (notifications.get() && debugMessages.get()) {
                    showToast("Command Skipped", "Auto-walk already " + (enabling ? "on" : "off"), Items.BARRIER);
                }
                return;
            }
            autoWalkEnabled = enabling;
            if (notifications.get()) {
                showToast("Movement Update",
                         enabling ? "Starting movement" : "Stopping movement",
                         Items.DIAMOND_BOOTS);
            }
        } else if (cmd.startsWith(".rotation set")) {
            for (Direction dir : Direction.values()) {
                if (cmd.endsWith(dir.command)) {
                    if (dir == currentDirection) {
                        if (notifications.get() && debugMessages.get()) {
                            showToast("Command Skipped", "Already facing " + dir.command, Items.BARRIER);
                        }
                        return;
                    }
                    currentDirection = dir;
                    break;
                }
            }
        }

        // Start command sequence with proper delays
        pendingCommand = cmd;
        currentTypedText = "";
        execState = CommandExecutionState.WAITING;
        execTimer = preCommandDelay.get();
    }

    private void toggleAutoWalk(boolean enable) {
        if (enable != autoWalkEnabled) {
            // Configure elytra speed if using elytra flight
            if (useElytraFlight.get() && enable) {
                queueCommand(".settings elytra-fly horizontal-speed " + elytraSpeed.get());
            }

            // Set auto-walk direction based on current direction
            if (enable && currentDirection != null) {
                String direction = switch(currentDirection) {
                    case NORTH -> "Forwards";
                    case SOUTH -> "Backwards";
                    case EAST -> "Right";
                    case WEST -> "Left";
                };
                queueCommand(".settings auto-walk simple-direction " + direction);
            }

            String cmd = ".toggle auto-walk " + (enable ? "on" : "off");
            // Only queue if not already pending
            if (commandQueue.stream().noneMatch(c -> c.equals(cmd))) {
                queueCommand(cmd);
            }
        }
    }


    private void updateMovementStats() {
        if (mc.player == null || lastPosition == null) return;

        Vec3d currentPos = mc.player.getPos();
        double dist = currentPos.distanceTo(lastPosition);

        // Update speed (blocks/second)
        currentSpeed = dist * 20; // Convert per-tick to per-second

        // Check if stuck
        if (dist < 0.01 && autoWalkEnabled) {
            stuckTicks++;
            if (stuckTicks >= STUCK_THRESHOLD) {
                handleStuckState();
            }
        } else {
            stuckTicks = 0;
            lastValidPosition = currentPos;
        }

        // Update tracking
        totalDistance += dist;
        lastPosition = currentPos;

        if (debugMessages.get()) {
            showDebugInfo();
        }

        // Show progress toast periodically
        if (showToasts.get() && System.currentTimeMillis() - lastToastTime > TOAST_COOLDOWN) {
            showProgressToast();
            lastToastTime = System.currentTimeMillis();
        }
    }

    private void handleStuckState() {
        if (lastValidPosition != null) {
            if (notifications.get()) {
                showToast("Recovery", String.format("Stuck for %ds - Resetting position", stuckTicks/20), Items.REDSTONE);
            }
            // Try to move back slightly
            mc.player.setPosition(
                lastValidPosition.x,
                mc.player.getY(),
                lastValidPosition.z
            );

            // Update status
            if (debugMessages.get()) {
                Vec3d current = mc.player.getPos();
                String posInfo = "Moved to: " +
                    String.format("X: %.1f, Y: %.1f, Z: %.1f",
                    current.x, current.y, current.z);
                showToast("Position Reset", posInfo, Items.COMPASS);
            }
            resetState();
        } else {
            showToast("Recovery Failed", "No valid position to return to", Items.BARRIER);
        }
    }

    private void showDebugInfo() {
        try {
            String status = String.format(
                "Pos: %d, %d, %d\nSpeed: %d b/s\nTotal: %,d m\nPattern: %s",
                (int)mc.player.getX(), (int)mc.player.getY(), (int)mc.player.getZ(),
                (int)currentSpeed, (int)totalDistance, searchPattern.get().name()
            );
            if (notifications.get()) {
                showToast("SearchBot Info", status, Items.COMPASS);
            }
        } catch (Exception e) {
            showToast("Debug Error", e.getMessage(), Items.BARRIER);
        }
    }

    private void showProgressToast() {
        String title = "SearchBot Progress";
        try {
            int visitedNodes = (int) gridNodes.stream().filter(PathNode::visited).count();
            int totalNodes = gridNodes.size();
            double nodePercentage = totalNodes > 0 ? (visitedNodes * 100.0) / totalNodes : 0;

            StringBuilder desc = new StringBuilder()
                .append("Nodes: ").append(visitedNodes).append("/").append(totalNodes)
                .append(" (").append(String.format("%.1f", nodePercentage)).append("%)")
                .append("\nDistance: ").append((int)totalDistance).append("m");

            // Add current target info if available
            if (currentNode != null) {
                desc.append("\nTarget: ").append(currentNode.pos().toShortString());
            }

            // Add minimum nodes info if set
            int minNodes = minNodesToVisit.get();
            if (minNodes > 0) {
                desc.append("\nMin Required: ").append(minNodes);
            }

            showToast(title, desc.toString(), Items.MAP);
        } catch (Exception e) {
            int visitedNodes = (int) gridNodes.stream().filter(PathNode::visited).count();
            String desc = "Nodes: " + visitedNodes + "/" + gridNodes.size() + "\n" +
                "Distance: " + Math.round(totalDistance) + "m";
            showToast(title, desc, Items.MAP);
        }
    }

    private void showFinalStats() {
        if (notifications.get()) {
            String title = "SearchBot Complete";
            long duration = System.currentTimeMillis() - startTime;

            try {
                int avgSpeed = duration > 0 ? (int)(totalDistance / (duration / 1000.0)) : 0;
                StringBuilder desc = new StringBuilder()
                    .append("Distance: ").append((int)totalDistance).append("m")
                    .append("\nTime: ").append(formatDuration(duration))
                    .append("\nChunks: ").append(exploredCount)
                    .append("\nSpeed: ").append(avgSpeed).append(" b/s");
                showToast(title, desc.toString(), Items.NETHER_STAR);
            } catch (Exception e) {
                StringBuilder desc = new StringBuilder()
                    .append("Distance: ").append(Math.round(totalDistance)).append("m")
                    .append("\nTime: ").append(formatDuration(duration))
                    .append("\nChunks: ").append(exploredCount);
                showToast(title, desc.toString(), Items.NETHER_STAR);
            }
        }
    }

    private void showToast(String title, String description, net.minecraft.item.Item icon) {
        if (showToasts.get()) {
            mc.getToastManager().add(new MeteorToast(icon, title, description));
        }
    }
    private void renderPathLines(Render3DEvent event) {
        List<PathNode> nodes = currentPath.nodes();
        for (int i = 0; i < nodes.size() - 1; i++) {
            BlockPos curr = nodes.get(i).pos();
            BlockPos next = nodes.get(i + 1).pos();
            event.renderer.line(
                curr.getX(), curr.getY(), curr.getZ(),
                next.getX(), next.getY(), next.getZ(),
                gridLineColor.get()
            );
        }
    }

    private void renderGridOverlay(Render3DEvent event) {
        // Render grid cells
        for (int x = minChunkX; x <= maxChunkX; x++) {
            for (int z = minChunkZ; z <= maxChunkZ; z++) {
                ChunkPos pos = new ChunkPos(x, z);
                SettingColor color;

                if (pos.equals(currentTarget)) {
                    color = currentTargetColor.get();
                } else if (completedChunks.contains(pos)) {
                    color = exploredColor.get();
                } else if (unexploredChunks.contains(pos)) {
                    color = newColor.get();
                } else {
                    continue;
                }

                int blockX = x << 4;
                int blockZ = z << 4;
                event.renderer.quad(
                    blockX, searchHeight.get(), blockZ,
                    blockX + 16, searchHeight.get(), blockZ,
                    blockX + 16, searchHeight.get(), blockZ + 16,
                    blockX, searchHeight.get(), blockZ + 16,
                    color
                );
            }
        }

        // Render grid lines
        for (int x = minChunkX; x <= maxChunkX + 1; x++) {
            int blockX = x << 4;
            event.renderer.line(
                blockX, searchHeight.get(), minChunkZ << 4,
                blockX, searchHeight.get(), (maxChunkZ + 1) << 4,
                gridLineColor.get()
            );
        }

        for (int z = minChunkZ; z <= maxChunkZ + 1; z++) {
            int blockZ = z << 4;
            event.renderer.line(
                minChunkX << 4, searchHeight.get(), blockZ,
                (maxChunkX + 1) << 4, searchHeight.get(), blockZ,
                gridLineColor.get()
            );
        }
    }

    private void renderCurrentNodePath(Render3DEvent event) {
        BlockPos pos = currentNode.pos();
        Vec3d playerPos = mc.player.getPos();
        event.renderer.line(
            playerPos.x, playerPos.y, playerPos.z,
            pos.getX(), pos.getY(), pos.getZ(),
            currentTargetColor.get()
        );
    }

    /**
     * Renders a bright line from player to the next target node
     */
    private void renderTargetLine(Render3DEvent event) {
        if (currentNode == null || mc.player == null) return;

        Vec3d playerPos = mc.player.getPos();
        BlockPos targetPos = currentNode.pos();
        Vec3d targetVec = Vec3d.ofCenter(targetPos);

        // Render a bright line to the target
        event.renderer.line(
            playerPos.x, playerPos.y + 1.0, playerPos.z,
            targetVec.x, targetVec.y + 1.0, targetVec.z,
            currentTargetColor.get()
        );

        // Render a small marker at the target
        event.renderer.box(
            targetVec.x - 0.5, targetVec.y + 0.5, targetVec.z - 0.5,
            targetVec.x + 0.5, targetVec.y + 1.5, targetVec.z + 0.5,
            currentTargetColor.get(),
            currentTargetColor.get(),
            meteordevelopment.meteorclient.renderer.ShapeMode.Both,
            0
        );
    }

    private String formatDuration(long ms) {
        long totalSeconds = ms / 1000;
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;
        long hours = minutes / 60;
        minutes = minutes % 60;

        StringBuilder time = new StringBuilder();
        if (hours > 0) {
            time.append(hours < 10 ? "0" : "").append(hours).append(":");
        }
        time.append(minutes < 10 ? "0" : "").append(minutes).append(":")
            .append(seconds < 10 ? "0" : "").append(seconds);
        return time.toString();
    }

    public enum FlightMode {
        VANILLA,
        PITCH40,
        BARITONE
    }
}
